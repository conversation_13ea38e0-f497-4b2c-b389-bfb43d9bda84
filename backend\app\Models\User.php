<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Permission\Traits\HasRoles;
use <PERSON>mon\JWTAuth\Contracts\JWTSubject;

class User extends Authenticatable implements JWTSubject
{
    use Notifiable, HasRoles, SoftDeletes;

    protected $fillable = [
        'name', 'username', 'email', 'phone', 'password', 'status',
        'photo', 'last_login_at', 'last_login_ip', 'is_active', 'role',
    ];

    protected $appends = ['role_names', 'permission_names'];

    protected $hidden = ['password', 'remember_token'];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    protected $guard_name = 'api';

    public function getPermissionNamesAttribute()
    {
        return $this->getAllPermissions()->pluck('name');
    }

    public function getRoleNamesAttribute()
    {
        return $this->roles()->pluck('name')->toArray();
    }

    public function roles()
    {
        return $this->belongsToMany(\Spatie\Permission\Models\Role::class, 'model_has_roles', 'model_id', 'role_id');
    }

    public function assignRole($role)
    {
        // Accept string, Role model, or array (take first if array)
        if (is_array($role)) {
            $role = count($role) > 0 ? $role[0] : null;
        }
        if (is_string($role)) {
            $role = \Spatie\Permission\Models\Role::firstOrCreate(['name' => $role, 'guard_name' => 'api']);
        }
        if ($role && is_object($role)) {
            \Illuminate\Support\Facades\DB::table('model_has_roles')->updateOrInsert(
                [
                    'model_id' => $this->id,
                    'model_type' => get_class($this),
                    'role_id' => $role->id
                ],
                [
                    'model_id' => $this->id,
                    'model_type' => get_class($this),
                    'role_id' => $role->id
                ]
            );
            $this->update(['role' => $role->name]);
        }
    }

    public function hasRole($role)
    {
        return $this->roles()->where('name', $role)->exists() || $this->role === $role;
    }

    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    public function getJWTCustomClaims()
    {
        return [
            'is_active' => $this->is_active
        ];
    }

    // Task relationships
    public function assignedTasks()
    {
        return $this->hasMany(Task::class, 'assigned_to');
    }

    public function createdTasks()
    {
        return $this->hasMany(Task::class, 'created_by');
    }

    // Branch relationships
    public function branchUsers()
    {
        return $this->hasMany(BranchUser::class);
    }

    public function branches()
    {
        return $this->hasManyThrough(
            Branch::class,
            BranchUser::class,
            'user_id', // Foreign key on branch_users table
            'branch_id', // Foreign key on branches table
            'id', // Local key on users table
            'branch_id' // Local key on branch_users table
        )->where('branch_users.is_active', true);
    }
}