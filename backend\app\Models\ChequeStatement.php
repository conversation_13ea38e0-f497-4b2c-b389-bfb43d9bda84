<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ChequeStatement extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'payment_id',
        'voucher_no',
        'transaction_id',
        'transaction_type',
        'reference_no',
        'refer_type',
        'refer_id',
        'refer_name',
        'amount',
        'payment_date',
        'cheque_no',
        'bank_name',
        'issue_date',
        'note',
        'status'
    ];

    protected $casts = [
        'payment_date' => 'date',
        'issue_date' => 'date',
        'amount' => 'decimal:2',
    ];

    // Relationship with Payment
    public function payment()
    {
        return $this->belongsTo(Payment::class);
    }

    // Relationship with Customer
    public function customer()
    {
        return $this->belongsTo(Customer::class, 'refer_id', 'id');
    }

    // Relationship with Supplier
    public function supplier()
    {
        return $this->belongsTo(Supplier::class, 'refer_id', 'id');
    }

    // Relationship with StaffLedger
    public function staffLedger()
    {
        return $this->belongsTo(StaffLedger::class, 'refer_id', 'id');
    }

    // Scope for pending cheques
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    // Scope for completed cheques
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    // Scope for declined cheques
    public function scopeDeclined($query)
    {
        return $query->where('status', 'declined');
    }
}
