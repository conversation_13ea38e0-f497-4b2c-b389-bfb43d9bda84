<?php

namespace App\Http\Controllers;

use App\Models\ChequeStatement;
use App\Models\Payment;
use App\Models\Sale;
use App\Models\Purchase;
use App\Models\Invoice;
use App\Models\PaymentMethod;
use App\Models\StaffLedger;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ChequeStatementController extends Controller
{
    /**
     * Display a listing of cheque statements.
     */
    public function index(Request $request)
    {
        try {
            $query = ChequeStatement::query();

            // Filter by status if provided
            if ($request->has('status') && $request->status !== '') {
                $query->where('status', $request->status);
            }

            // Filter by date range if provided
            if ($request->has('from_date') && $request->from_date !== '') {
                $query->whereDate('payment_date', '>=', $request->from_date);
            }

            if ($request->has('to_date') && $request->to_date !== '') {
                $query->whereDate('payment_date', '<=', $request->to_date);
            }

            // Filter by refer_type if provided
            if ($request->has('refer_type') && $request->refer_type !== '') {
                $query->where('refer_type', $request->refer_type);
            }

            // Get the results and load relationships conditionally
            $chequeStatements = $query->orderBy('payment_date', 'desc')->get();

            // Load relationships conditionally after getting the results
            foreach ($chequeStatements as $statement) {
                try {
                    if ($statement->refer_type === 'Customer' && $statement->refer_id) {
                        $statement->load('customer');
                    } elseif ($statement->refer_type === 'Supplier' && $statement->refer_id) {
                        $statement->load('supplier');
                    } elseif ($statement->refer_type === 'Ledger' && $statement->refer_id) {
                        $statement->load('staffLedger');
                    }
                } catch (\Exception $e) {
                    Log::warning("Failed to load relationship for cheque statement {$statement->id}: " . $e->getMessage());
                }
            }

            return response()->json([
                'success' => true,
                'data' => $chequeStatements
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch cheque statements',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update cheque status to completed.
     */
    public function markAsCompleted($id)
    {
        try {
            $chequeStatement = ChequeStatement::findOrFail($id);
            
            if ($chequeStatement->status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only pending cheques can be marked as completed'
                ], 400);
            }

            $chequeStatement->update(['status' => 'completed']);

            return response()->json([
                'success' => true,
                'message' => 'Cheque marked as completed successfully',
                'data' => $chequeStatement
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update cheque status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update cheque status to declined.
     */
    public function markAsDeclined($id)
    {
        try {
            $chequeStatement = ChequeStatement::findOrFail($id);

            if ($chequeStatement->status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only pending cheques can be marked as declined'
                ], 400);
            }

            // Start database transaction
            DB::beginTransaction();

            // Add amount back to the original source based on transaction type
            $this->addAmountBackToSource($chequeStatement);

            // Update cheque status to declined
            $chequeStatement->update(['status' => 'declined']);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Cheque marked as declined successfully and amount added back to source',
                'data' => $chequeStatement
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('ChequeStatement decline error: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());
            return response()->json([
                'success' => false,
                'message' => 'Failed to update cheque status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get cheque statement statistics.
     */
    public function getStatistics()
    {
        try {
            $stats = [
                'total_cheques' => ChequeStatement::count(),
                'pending_cheques' => ChequeStatement::where('status', 'pending')->count(),
                'completed_cheques' => ChequeStatement::where('status', 'completed')->count(),
                'declined_cheques' => ChequeStatement::where('status', 'declined')->count(),
                'total_amount' => ChequeStatement::sum('amount'),
                'pending_amount' => ChequeStatement::where('status', 'pending')->sum('amount'),
                'completed_amount' => ChequeStatement::where('status', 'completed')->sum('amount'),
                'declined_amount' => ChequeStatement::where('status', 'declined')->sum('amount'),
            ];

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create cheque statement from payment (used internally by controllers).
     */
    public static function createFromPayment(Payment $payment)
    {
        if ($payment->payment_method === 'Cheque' || $payment->payment_method === 'cheque') {
            return ChequeStatement::create([
                'payment_id' => $payment->id,
                'voucher_no' => $payment->voucher_no,
                'transaction_id' => $payment->transaction_id, // This can now be null for opening_balance
                'transaction_type' => $payment->transaction_type,
                'reference_no' => $payment->reference_no,
                'refer_type' => $payment->refer_type,
                'refer_id' => $payment->refer_id,
                'refer_name' => $payment->refer_name,
                'amount' => $payment->amount,
                'payment_date' => $payment->payment_date,
                'cheque_no' => $payment->cheque_no,
                'bank_name' => $payment->bank_name,
                'issue_date' => $payment->issue_date,
                'note' => $payment->note,
                'status' => 'pending'
            ]);
        }
        return null;
    }

    /**
     * Add amount back to the original source when cheque is declined.
     */
    private function addAmountBackToSource(ChequeStatement $chequeStatement)
    {
        $transactionType = $chequeStatement->transaction_type;
        $transactionId = $chequeStatement->transaction_id;
        $amount = $chequeStatement->amount;

        Log::info("Adding amount back to source: Type={$transactionType}, ID={$transactionId}, Amount={$amount}");

        switch ($transactionType) {
            case 'sale':
                $this->addAmountBackToSale($transactionId, $amount);
                break;
            case 'purchase':
                $this->addAmountBackToPurchase($transactionId, $amount);
                break;
            case 'invoice':
                $this->addAmountBackToInvoice($transactionId, $amount);
                break;
            case 'ledger':
                $this->addAmountBackToLedger($transactionId, $amount, $chequeStatement);
                break;
            case 'opening_balance':
                // For opening_balance, transaction_id is null, so we don't need to add amount back to any specific transaction
                // The declined cheque will be reflected in the statement generation through the cheque_statements table
                Log::info("Opening balance cheque declined: Amount={$amount}, Voucher={$chequeStatement->voucher_no}");
                break;
            default:
                throw new \Exception("Unknown transaction type: {$transactionType}");
        }
    }

    /**
     * Add amount back to sales table.
     */
    private function addAmountBackToSale($saleId, $amount)
    {
        try {
            $sale = Sale::findOrFail($saleId);
            Log::info("Found sale: ID={$saleId}, Bill={$sale->bill_number}, Current received={$sale->received_amount}, Current balance={$sale->balance_amount}");

            // Reduce received amount and increase balance amount
            $sale->received_amount = max(0, $sale->received_amount - $amount);
            $sale->balance_amount = $sale->balance_amount + $amount;

            // Update status based on new amounts
            if ($sale->balance_amount >= $sale->total) {
                $sale->status = 'pending';
            } elseif ($sale->balance_amount > 0) {
                $sale->status = 'partial';
            } else {
                $sale->status = 'paid';
            }

            $sale->save();
            Log::info("Updated sale: New received={$sale->received_amount}, New balance={$sale->balance_amount}, Status={$sale->status}");

            // Do NOT update payment_methods table on cheque decline
            // $updated = PaymentMethod::where('type', 'Sales')
            //     ->where('reference_number', $sale->bill_number)
            //     ->update([
            //         'settled_amount' => $sale->received_amount,
            //         'balance_amount' => $sale->balance_amount,
            //     ]);
            // Log::info("Updated payment_methods table: {$updated} rows affected");
        } catch (\Exception $e) {
            Log::error("Error in addAmountBackToSale: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Add amount back to purchases table.
     */
    private function addAmountBackToPurchase($purchaseId, $amount)
    {
        try {
            $purchase = Purchase::findOrFail($purchaseId);
            Log::info("Found purchase: ID={$purchaseId}, Bill={$purchase->bill_number}, Current paid={$purchase->paid_amount}, Total={$purchase->total}");

            // Reduce paid amount
            $purchase->paid_amount = max(0, $purchase->paid_amount - $amount);
            $balanceAmount = $purchase->total - $purchase->paid_amount;

            // Update status based on new amounts
            if ($purchase->paid_amount <= 0) {
                $purchase->status = 'pending';
            } elseif ($balanceAmount > 0) {
                $purchase->status = 'pending'; // Use 'pending' instead of 'partial' as it's not in enum
            } else {
                $purchase->status = 'paid';
            }

            $purchase->save();
            Log::info("Updated purchase: New paid={$purchase->paid_amount}, New balance={$balanceAmount}, Status={$purchase->status}");

            // Do NOT update payment_methods table on cheque decline
            // $updated = PaymentMethod::where('type', 'Purchase')
            //     ->where('reference_number', $purchase->bill_number)
            //     ->update([
            //         'settled_amount' => $purchase->paid_amount,
            //         'balance_amount' => $balanceAmount,
            //     ]);
            // Log::info("Updated payment_methods table: {$updated} rows affected");
        } catch (\Exception $e) {
            Log::error("Error in addAmountBackToPurchase: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Add amount back to invoices table.
     */
    private function addAmountBackToInvoice($invoiceId, $amount)
    {
        try {
            $invoice = Invoice::findOrFail($invoiceId);
            Log::info("Found invoice: ID={$invoiceId}, Invoice={$invoice->invoice_no}, Current purchase_amount={$invoice->purchase_amount}, Current balance={$invoice->balance}");

            // Reduce purchase amount and increase balance
            $invoice->purchase_amount = max(0, $invoice->purchase_amount - $amount);
            $invoice->balance = $invoice->balance + $amount;

            // Update status based on new amounts
            if ($invoice->balance >= $invoice->total_amount) {
                $invoice->status = 'pending';
            } elseif ($invoice->balance > 0) {
                $invoice->status = 'partial';
            } else {
                $invoice->status = 'paid';
            }

            $invoice->save();
            Log::info("Updated invoice: New purchase_amount={$invoice->purchase_amount}, New balance={$invoice->balance}, Status={$invoice->status}");

            // Do NOT update payment_methods table on cheque decline
            // $updated = PaymentMethod::where('type', 'Invoice')
            //     ->where('reference_number', $invoice->invoice_no)
            //     ->update([
            //         'settled_amount' => $invoice->purchase_amount,
            //         'balance_amount' => $invoice->balance,
            //     ]);
            // Log::info("Updated payment_methods table: {$updated} rows affected");
        } catch (\Exception $e) {
            Log::error("Error in addAmountBackToInvoice: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Add amount back to ledger (for ledger transactions, no specific table update needed).
     */
    private function addAmountBackToLedger($ledgerId, $amount, ChequeStatement $chequeStatement)
    {
        try {
            $staffLedger = StaffLedger::findOrFail($ledgerId);
            Log::info("Found staff ledger: ID={$ledgerId}, Name={$staffLedger->name}, Account Group={$staffLedger->account_group}");

            // For ledger transactions, we don't need to update any balance in the staff_ledger table
            // The declined cheque will be handled in the statement generation logic
            // Just log the action for audit purposes
            Log::info("Ledger cheque declined: Amount={$amount}, Voucher={$chequeStatement->voucher_no}, Account={$staffLedger->account_group}");

            // Note: Unlike sales/purchases/invoices, ledger transactions don't have balance fields to update
            // The declined status will be reflected in the statement generation through the cheque_statements table
        } catch (\Exception $e) {
            Log::error("Error in addAmountBackToLedger: " . $e->getMessage());
            throw $e;
        }
    }
}
