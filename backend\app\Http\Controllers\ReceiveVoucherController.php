<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\ChequeStatement;

class ReceiveVoucherController extends Controller
{
    // List all receive voucher transactions
    public function index()
    {
        $vouchers = Payment::where('voucher_no', 'like', 'REC-%')->orderByDesc('id')->get();
        return response()->json(['success' => true, 'data' => $vouchers]);
    }

    // View a specific receive voucher transaction
    public function show($id)
    {
        $voucher = Payment::where('voucher_no', 'like', 'REC-%')->find($id);
        if (!$voucher) {
            return response()->json(['success' => false, 'message' => 'Voucher not found'], 404);
        }
        return response()->json(['success' => true, 'data' => $voucher]);
    }

    // Delete a specific receive voucher transaction
    public function destroy($id)
    {
        $voucher = Payment::where('voucher_no', 'like', 'REC-%')->find($id);
        if (!$voucher) {
            return response()->json(['success' => false, 'message' => 'Voucher not found'], 404);
        }

        DB::beginTransaction();
        try {
            // Restore outstanding for sale
            if ($voucher->transaction_type === 'sale' && $voucher->transaction_id) {
                $sale = \App\Models\Sale::find($voucher->transaction_id);
                if ($sale) {
                    $sale->received_amount = max(0, $sale->received_amount - $voucher->amount);
                    $sale->balance_amount = $sale->total - $sale->received_amount;
                    if ($sale->received_amount <= 0) {
                        $sale->status = 'pending';
                    } elseif ($sale->received_amount < $sale->total) {
                        $sale->status = 'partial';
                    } else {
                        $sale->status = 'paid';
                    }
                    $sale->save();
                }
            }
            // Restore outstanding for invoice
            if ($voucher->transaction_type === 'invoice' && $voucher->transaction_id) {
                $invoice = \App\Models\Invoice::find($voucher->transaction_id);
                if ($invoice) {
                    $invoice->purchase_amount = max(0, $invoice->purchase_amount - $voucher->amount);
                    $invoice->balance = $invoice->total_amount - $invoice->purchase_amount;
                    if ($invoice->purchase_amount <= 0) {
                        $invoice->status = 'Pending';
                    } elseif ($invoice->purchase_amount < $invoice->total_amount) {
                        $invoice->status = 'Partial';
                    } else {
                        $invoice->status = 'Paid';
                    }
                    $invoice->save();
                }
            }
            // For opening_balance, nothing to update (OutstandingController calculates it live)

            // Soft delete related ChequeStatement if payment method is cheque
            if (strtolower($voucher->payment_method) === 'cheque') {
                $chequeStatement = ChequeStatement::where('payment_id', $voucher->id)->first();
                if ($chequeStatement) {
                    $chequeStatement->delete();
                }
            }

            // Accept deleted_by from request, fallback to authenticated user
            $deletedBy = request()->input('deleted_by') ?? request()->user()->id ?? auth()->id();
            if (!$deletedBy) {
                \Log::warning('No user ID found when deleting receive voucher', [
                    'request_user' => request()->user(),
                    'auth_id' => auth()->id(),
                ]);
                return response()->json(['message' => 'No authenticated or provided user found for delete action.'], 403);
            }
            $voucher->deleted_by = $deletedBy;
            $voucher->save();
            $voucher->delete();
            DB::commit();
            return response()->json(['success' => true, 'message' => 'Voucher deleted and outstanding restored successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['success' => false, 'message' => 'Error deleting voucher: ' . $e->getMessage()], 500);
        }
    }

    // List all soft-deleted receive voucher transactions
    public function deleted()
    {
        $vouchers = Payment::onlyTrashed()
            ->with('deletedByUser')
            ->where('voucher_no', 'like', 'REC-%')
            ->orderByDesc('id')
            ->get();
        
        \Log::info('Deleted receive vouchers found: ' . $vouchers->count());
        \Log::info('Deleted receive vouchers data: ' . $vouchers->toJson());
        
        return response()->json(['success' => true, 'data' => $vouchers->map(function($voucher) {
            return array_merge($voucher->toArray(), [
                'deleted_by_user' => $voucher->deletedByUser ? [
                    'id' => $voucher->deletedByUser->id,
                    'name' => $voucher->deletedByUser->name,
                    'email' => $voucher->deletedByUser->email,
                ] : null,
            ]);
        })]);
    }

    // Restore a soft-deleted receive voucher transaction
    public function restore($id)
    {
        $voucher = Payment::onlyTrashed()
            ->where('voucher_no', 'like', 'REC-%')
            ->find($id);
        
        if (!$voucher) {
            return response()->json(['success' => false, 'message' => 'Voucher not found'], 404);
        }

        DB::beginTransaction();
        try {
            // Restore the voucher
            $voucher->restore();

            // Update outstanding for sale
            if ($voucher->transaction_type === 'sale' && $voucher->transaction_id) {
                $sale = \App\Models\Sale::find($voucher->transaction_id);
                if ($sale) {
                    $sale->received_amount = min($sale->total, $sale->received_amount + $voucher->amount);
                    $sale->balance_amount = $sale->total - $sale->received_amount;
                    if ($sale->received_amount >= $sale->total) {
                        $sale->status = 'paid';
                    } elseif ($sale->received_amount > 0) {
                        $sale->status = 'partial';
                    } else {
                        $sale->status = 'pending';
                    }
                    $sale->save();
                }
            }
            // Update outstanding for invoice
            if ($voucher->transaction_type === 'invoice' && $voucher->transaction_id) {
                $invoice = \App\Models\Invoice::find($voucher->transaction_id);
                if ($invoice) {
                    $invoice->purchase_amount = min($invoice->total_amount, $invoice->purchase_amount + $voucher->amount);
                    $invoice->balance = $invoice->total_amount - $invoice->purchase_amount;
                    if ($invoice->purchase_amount >= $invoice->total_amount) {
                        $invoice->status = 'Paid';
                    } elseif ($invoice->purchase_amount > 0) {
                        $invoice->status = 'Partial';
                    } else {
                        $invoice->status = 'Pending';
                    }
                    $invoice->save();
                }
            }

            // Restore related ChequeStatement if payment method is cheque
            if (strtolower($voucher->payment_method) === 'cheque') {
                $chequeStatement = ChequeStatement::onlyTrashed()->where('payment_id', $voucher->id)->first();
                if ($chequeStatement) {
                    $chequeStatement->restore();
                }
            }

            DB::commit();
            return response()->json(['success' => true, 'message' => 'Receive voucher restored successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['success' => false, 'message' => 'Error restoring voucher: ' . $e->getMessage()], 500);
        }
    }

    // Force delete a receive voucher transaction
    public function forceDelete($id)
    {
        $voucher = Payment::onlyTrashed()
            ->where('voucher_no', 'like', 'REC-%')
            ->find($id);
        
        if (!$voucher) {
            return response()->json(['success' => false, 'message' => 'Voucher not found'], 404);
        }

        DB::beginTransaction();
        try {
            // Permanently delete related ChequeStatement if payment method is cheque
            if (strtolower($voucher->payment_method) === 'cheque') {
                $chequeStatement = ChequeStatement::onlyTrashed()->where('payment_id', $voucher->id)->first();
                if ($chequeStatement) {
                    $chequeStatement->forceDelete();
                }
            }

            // Permanently delete the voucher
            $voucher->forceDelete();

            DB::commit();
            return response()->json(['success' => true, 'message' => 'Receive voucher permanently deleted']);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['success' => false, 'message' => 'Error permanently deleting voucher: ' . $e->getMessage()], 500);
        }
    }
} 