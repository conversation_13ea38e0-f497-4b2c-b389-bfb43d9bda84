<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Customer extends Model
{
    use SoftDeletes;
    use HasFactory;

    protected $fillable = [
        'customer_name',
        'email',
        'phone',
        'address',
        'nic_number',
        'openingbalance',
        'photo',
        'loyalty_card_number',
        'card_name',
        'card_types', // JSON field for multiple types
        'valid_date',
    ];

    protected $casts = [
        'card_types' => 'array', // Cast JSON to array
    ];

    protected $appends = ['photo_url'];

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'deleted_at',
    ];

    /**
     * Get the photo URL.
     */
    public function getPhotoUrlAttribute()
    {
        return $this->photo ? asset('storage/' . $this->photo) : null;
    }

    /**
     * Get the photo attribute.
     */
    public function getPhotoAttribute($value)
    {
        return $value;
    }

    /**
     * Relationship to the user who deleted the customer.
     */
    public function deletedByUser()
    {
        return $this->belongsTo(User::class, 'deleted_by')->withTrashed();
    }
}