<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('payments', function (Blueprint $table) {
            $table->decimal('opening_balance', 10, 2)->default(0.00);
            $table->unsignedBigInteger('transaction_id')->nullable()->change(); // Make transaction_id nullable
        });
    }

    public function down()
    {
        Schema::table('payments', function (Blueprint $table) {
            $table->dropColumn('opening_balance');
            $table->unsignedBigInteger('transaction_id')->change();
        });
    }
};
