<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\Product;
use App\Models\StockTransfer;
use App\Models\StockTransferItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class StockTransferController extends Controller
{
    public function index(Request $request)
    {
        try {
            $fromDate = $request->query('from', Carbon::now()->subMonth()->toDateString());
            $toDate = $request->query('to', Carbon::now()->toDateString());

            $transfers = StockTransfer::with(['branch', 'items.product'])
                ->whereBetween('transfer_date', [$fromDate, $toDate])
                ->get()
                ->map(function ($transfer) {
                    return [
                        'id' => $transfer->id,
                        'transfer_number' => $transfer->transfer_number,
                        'branch_id' => $transfer->branch_id,
                        'branch_name' => $transfer->branch->branch_name ?? 'Unknown',
                        'branch' => [
                            'branch_id' => $transfer->branch->branch_id ?? null,
                            'branch_name' => $transfer->branch->branch_name ?? 'Unknown',
                            'branch_address' => $transfer->branch->branch_address ?? '',
                            'authorized_person' => $transfer->branch->authorized_person ?? '',
                            'phone_number' => $transfer->branch->phone_number ?? '',
                        ],
                        'transfer_date' => $transfer->transfer_date,
                        'total_quantity' => $transfer->total_quantity,
                        'total_value' => $transfer->total_value,
                        'status' => $transfer->status,
                        'items' => $transfer->items->map(function ($item) {
                            return [
                                'id' => $item->id,
                                'product_id' => $item->product_id,
                                'product_name' => $item->product->product_name ?? $item->description,
                                'quantity' => $item->quantity,
                                'unit_price' => $item->unit_price,
                                'sales_price' => $item->sales_price,
                                'discount_amount' => $item->discount_amount,
                                'total' => $item->total,
                                'supplier' => $item->supplier,
                                'category' => $item->category,
                                'store_location' => $item->store_location,
                                'mrp' => $item->mrp,
                            ];
                        })->toArray(),
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $transfers
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching stock transfers',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'transfer.id' => 'required|string|unique:stock_transfers,transfer_number',
            'transfer.date' => 'required|date',
            'branch.id' => 'required|exists:branches,branch_id',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,product_id',
            'items.*.description' => 'required|string',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.sales_price' => 'required|numeric|min:0',
            'items.*.discount_amount' => 'nullable|numeric|min:0',
            'items.*.total' => 'required|numeric|min:0',
            'items.*.mrp' => 'required|numeric|min:0',
            'items.*.supplier' => 'nullable|string',
            'items.*.category' => 'nullable|string',
            'items.*.store_location' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $data = $request->all();
            $transfer = StockTransfer::create([
                'transfer_number' => $data['transfer']['id'],
                'branch_id' => $data['branch']['id'],
                'transfer_date' => $data['transfer']['date'],
                'total_quantity' => collect($data['items'])->sum('quantity'),
                'total_value' => collect($data['items'])->sum('total'),
                'status' => 'Pending'
            ]);

            foreach ($data['items'] as $item) {
                StockTransferItem::create([
                    'stock_transfer_id' => $transfer->id,
                    'product_id' => $item['product_id'],
                    'description' => $item['description'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'sales_price' => $item['sales_price'],
                    'discount_amount' => $item['discount_amount'] ?? 0,
                    'total' => $item['total'],
                    'supplier' => $item['supplier'] ?? null,
                    'category' => $item['category'] ?? null,
                    'store_location' => $item['store_location'] ?? null,
                    'mrp' => $item['mrp'],
                ]);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Stock transfer created successfully',
                'data' => $transfer->load(['branch', 'items.product'])
            ], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Error creating stock transfer',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function update(Request $request, $id)
    {
        $transfer = StockTransfer::findOrFail($id);
        $validator = Validator::make($request->all(), [
            'transfer.id' => 'required|string|unique:stock_transfers,transfer_number,' . $id,
            'transfer.date' => 'required|date',
            'branch.id' => 'required|exists:branches,branch_id',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,product_id',
            'items.*.description' => 'required|string',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.sales_price' => 'required|numeric|min:0',
            'items.*.discount_amount' => 'nullable|numeric|min:0',
            'items.*.total' => 'required|numeric|min:0',
            'items.*.mrp' => 'required|numeric|min:0',
            'items.*.supplier' => 'nullable|string',
            'items.*.category' => 'nullable|string',
            'items.*.store_location' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $data = $request->all();
            $transfer->update([
                'transfer_number' => $data['transfer']['id'],
                'branch_id' => $data['branch']['id'],
                'transfer_date' => $data['transfer']['date'],
                'total_quantity' => collect($data['items'])->sum('quantity'),
                'total_value' => collect($data['items'])->sum('total'),
                'status' => $data['status'] ?? $transfer->status
            ]);

            // Delete old items
            $transfer->items()->delete();

            // Create new items
            foreach ($data['items'] as $item) {
                StockTransferItem::create([
                    'stock_transfer_id' => $transfer->id,
                    'product_id' => $item['product_id'],
                    'description' => $item['description'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'sales_price' => $item['sales_price'],
                    'discount_amount' => $item['discount_amount'] ?? 0,
                    'total' => $item['total'],
                    'supplier' => $item['supplier'] ?? null,
                    'category' => $item['category'] ?? null,
                    'store_location' => $item['store_location'] ?? null,
                    'mrp' => $item['mrp'],
                ]);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Stock transfer updated successfully',
                'data' => $transfer->load(['branch', 'items.product'])
            ], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Error updating stock transfer',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy($id)
    {
        try {
            $transfer = StockTransfer::findOrFail($id);

            DB::beginTransaction();

            $transfer->items()->delete();
            $transfer->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Stock transfer deleted successfully'
            ], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Error deleting stock transfer',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Get pending transfers for a specific branch
     */
    public function pendingForBranch(Request $request, $branchId)
    {
        try {
            \Log::debug("Fetching pending transfers for branch ID: {$branchId}");
            $fromDate = $request->query('from', Carbon::now()->subMonth()->toDateString());
            $toDate = $request->query('to', Carbon::now()->toDateString());
            \Log::debug("Date range: {$fromDate} to {$toDate}");
            
            // Get branch info
            $branch = Branch::find($branchId);
            if (!$branch) {
                \Log::error("Branch not found with ID: {$branchId}");
                return response()->json([
                    'success' => false,
                    'message' => 'Branch not found'
                ], 404);
            }
            
            \Log::debug("Found branch: " . $branch->branch_name);

            // Get transfers that are pending for this branch
            $query = StockTransfer::with(['branch', 'items.product'])
                ->where('branch_id', $branchId)
                ->where('status', 'Pending')
                ->whereBetween('transfer_date', [$fromDate, $toDate]);
                
            \Log::debug("SQL query: " . $query->toSql());
            \Log::debug("Query bindings: " . json_encode($query->getBindings()));
            
            $transfers = $query->get();
            \Log::debug("Found " . count($transfers) . " pending transfers");
            
            $transfers = $transfers->map(function ($transfer) {
                    // Get the branch name that initiated the transfer (from warehouse or another branch)
                    $fromBranchName = $transfer->from_branch_id 
                        ? Branch::find($transfer->from_branch_id)->branch_name ?? 'Unknown Branch'
                        : 'Main Warehouse';
                        
                    return [
                        'id' => $transfer->id,
                        'transfer_number' => $transfer->transfer_number,
                        'branch_id' => $transfer->branch_id,
                        'branch_name' => $transfer->branch->branch_name ?? 'Unknown',
                        'from_branch_id' => $transfer->from_branch_id,
                        'from_branch_name' => $fromBranchName,
                        'transfer_date' => $transfer->transfer_date,
                        'total_quantity' => $transfer->total_quantity,
                        'total_value' => $transfer->total_value,
                        'status' => $transfer->status,
                        'items' => $transfer->items->map(function ($item) {
                            return [
                                'id' => $item->id,
                                'product_id' => $item->product_id,
                                'product_name' => $item->product->product_name ?? $item->description,
                                'quantity' => $item->quantity,
                                'unit_price' => $item->unit_price,
                                'sales_price' => $item->sales_price,
                                'discount_amount' => $item->discount_amount,
                                'total' => $item->total,
                                'supplier' => $item->supplier,
                                'category' => $item->category,
                                'store_location' => $item->store_location,
                                'mrp' => $item->mrp,
                            ];
                        })->toArray(),
                    ];
                });

            \Log::debug("Returning " . count($transfers) . " formatted transfers");
            
            // Log a sample of the first transfer if available (for debugging)
            if (count($transfers) > 0) {
                \Log::debug("Sample transfer data: " . json_encode(array_slice($transfers->toArray(), 0, 1)));
            }

            return response()->json([
                'success' => true,
                'data' => $transfers
            ], 200);
        } catch (\Exception $e) {
            \Log::error("Error in pendingForBranch: " . $e->getMessage());
            \Log::error("Exception trace: " . $e->getTraceAsString());
            
            return response()->json([
                'success' => false,
                'message' => 'Error fetching pending stock transfers',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Mark a transfer as received and update inventory
     */
    public function receiveTransfer(Request $request, $id)
    {
        try {
            \Log::debug("Receiving transfer with ID: {$id}");
            \Log::debug("Request data: " . json_encode($request->all()));
            
            $validator = Validator::make($request->all(), [
                'branch_id' => 'required|exists:branches,branch_id',
            ]);

            if ($validator->fails()) {
                \Log::error("Validation failed: " . json_encode($validator->errors()));
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
            
            $branchId = $request->branch_id;
            \Log::debug("Looking for transfer {$id} to be received by branch {$branchId}");
            
            $transfer = StockTransfer::with('items.product')->findOrFail($id);
            \Log::debug("Found transfer: " . json_encode([
                'id' => $transfer->id,
                'branch_id' => $transfer->branch_id,
                'status' => $transfer->status,
                'item_count' => $transfer->items->count()
            ]));
            
            // Verify the transfer is for the correct branch
            if ($transfer->branch_id != $branchId) {
                \Log::error("Transfer branch ID {$transfer->branch_id} doesn't match requesting branch {$branchId}");
                return response()->json([
                    'success' => false,
                    'message' => 'This transfer is not assigned to your branch'
                ], 403);
            }
            
            // Check if already received
            if ($transfer->status !== 'Pending') {
                \Log::error("Transfer status is {$transfer->status}, not Pending");
                return response()->json([
                    'success' => false,
                    'message' => 'This transfer has already been processed'
                ], 400);
            }
            
            DB::beginTransaction();
            
            // Update stock quantities for each item in the branch inventory
            foreach ($transfer->items as $item) {
                // Implement logic to update branch inventory
                // This should connect to your inventory management system
                // For now, we'll just simulate this by marking the transfer as received
                
                // Example inventory update logic (customize based on your actual inventory model):
                // $branchInventory = BranchInventory::firstOrNew([
                //     'branch_id' => $branchId,
                //     'product_id' => $item->product_id
                // ]);
                // $branchInventory->quantity += $item->quantity;
                // $branchInventory->save();
            }
            
            // Update transfer status to Received
            $transfer->status = 'Accepted';
            $transfer->received_date = Carbon::now();
            $transfer->received_by = auth()->id(); // If using authentication
            $transfer->save();
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'message' => 'Stock transfer received successfully',
                'data' => $transfer
            ], 200);
            
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Error receiving stock transfer',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}