<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\ChequeStatement;

class PaymentVoucherController extends Controller
{
    // List all payment voucher transactions
    public function index()
    {
        try {
            $vouchers = Payment::where('voucher_no', 'like', 'PAY-%')->orderByDesc('id')->get();
            return response()->json(['success' => true, 'data' => $vouchers]);
        } catch (\Exception $e) {
            Log::error('Error fetching payment vouchers: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to fetch payment vouchers'], 500);
        }
    }

    // View a specific payment voucher transaction
    public function show($id)
    {
        try {
            $voucher = Payment::where('voucher_no', 'like', 'PAY-%')->find($id);
            if (!$voucher) {
                return response()->json(['success' => false, 'message' => 'Voucher not found'], 404);
            }
            return response()->json(['success' => true, 'data' => $voucher]);
        } catch (\Exception $e) {
            Log::error('Error fetching payment voucher: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to fetch voucher details'], 500);
        }
    }

    // Delete a specific payment voucher transaction
    public function destroy($id)
    {
        try {
            $voucher = Payment::where('voucher_no', 'like', 'PAY-%')->find($id);
            if (!$voucher) {
                return response()->json(['success' => false, 'message' => 'Voucher not found'], 404);
            }

            DB::beginTransaction();

            // Restore outstanding for purchase
            if ($voucher->transaction_type === 'purchase' && $voucher->transaction_id) {
                $purchase = class_exists(\App\Models\Purchase::class)
                    ? \App\Models\Purchase::find($voucher->transaction_id)
                    : null;
                if ($purchase) {
                    $purchase->paid_amount = max(0, $purchase->paid_amount - $voucher->amount);
                    $purchase->status = $purchase->paid_amount <= 0 ? 'pending' : 'paid';
                    $purchase->save();
                } else {
                    Log::warning("Purchase with ID {$voucher->transaction_id} not found for voucher {$id}");
                }
            }

            // Restore outstanding for invoice
            if ($voucher->transaction_type === 'invoice' && $voucher->transaction_id) {
                $invoice = class_exists(\App\Models\Invoice::class)
                    ? \App\Models\Invoice::find($voucher->transaction_id)
                    : null;
                if ($invoice) {
                    $invoice->purchase_amount = max(0, $invoice->purchase_amount - $voucher->amount);
                    $invoice->balance = $invoice->total_amount - $invoice->purchase_amount;
                    $invoice->status = $invoice->purchase_amount <= 0 ? 'Pending' :
                        ($invoice->purchase_amount < $invoice->total_amount ? 'Partial' : 'Paid');
                    $invoice->save();
                } else {
                    Log::warning("Invoice with ID {$voucher->transaction_id} not found for voucher {$id}");
                }
            }

            // Soft delete related ChequeStatement if payment method is cheque
            if (strtolower($voucher->payment_method) === 'cheque') {
                $chequeStatement = ChequeStatement::where('payment_id', $voucher->id)->first();
                if ($chequeStatement) {
                    $chequeStatement->delete();
                }
            }

            // Accept deleted_by from request, fallback to authenticated user
            $deletedBy = request()->input('deleted_by') ?? request()->user()->id ?? auth()->id();
            if (!$deletedBy) {
                Log::warning('No user ID found when deleting payment voucher', [
                    'request_user' => request()->user(),
                    'auth_id' => auth()->id(),
                ]);
                return response()->json(['message' => 'No authenticated or provided user found for delete action.'], 403);
            }
            $voucher->deleted_by = $deletedBy;
            $voucher->save();
            $voucher->delete();
            DB::commit();
            return response()->json(['success' => true, 'message' => 'Voucher deleted successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error deleting payment voucher: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error deleting voucher: ' . $e->getMessage()], 500);
        }
    }

    // List all soft-deleted payment voucher transactions
    public function deleted()
    {
        $vouchers = Payment::onlyTrashed()
            ->with('deletedByUser')
            ->where('voucher_no', 'like', 'PAY-%')
            ->orderByDesc('id')
            ->get();
        
        Log::info('Deleted payment vouchers found: ' . $vouchers->count());
        Log::info('Deleted payment vouchers data: ' . $vouchers->toJson());
        
        return response()->json(['success' => true, 'data' => $vouchers->map(function($voucher) {
            return array_merge($voucher->toArray(), [
                'deleted_by_user' => $voucher->deletedByUser ? [
                    'id' => $voucher->deletedByUser->id,
                    'name' => $voucher->deletedByUser->name,
                    'email' => $voucher->deletedByUser->email,
                ] : null,
            ]);
        })]);
    }

    // Restore a soft-deleted payment voucher transaction
    public function restore($id)
    {
        $voucher = Payment::onlyTrashed()
            ->where('voucher_no', 'like', 'PAY-%')
            ->find($id);
        
        if (!$voucher) {
            return response()->json(['success' => false, 'message' => 'Voucher not found'], 404);
        }

        DB::beginTransaction();
        try {
            // Restore the voucher
            $voucher->restore();

            // Update outstanding for purchase
            if ($voucher->transaction_type === 'purchase' && $voucher->transaction_id) {
                $purchase = \App\Models\Purchase::find($voucher->transaction_id);
                if ($purchase) {
                    $purchase->paid_amount = min($purchase->total, $purchase->paid_amount + $voucher->amount);
                    $purchase->balance_amount = $purchase->total - $purchase->paid_amount;
                    if ($purchase->paid_amount >= $purchase->total) {
                        $purchase->status = 'paid';
                    } elseif ($purchase->paid_amount > 0) {
                        $purchase->status = 'partial';
                    } else {
                        $purchase->status = 'pending';
                    }
                    $purchase->save();
                }
            }

            // Restore related ChequeStatement if payment method is cheque
            if (strtolower($voucher->payment_method) === 'cheque') {
                $chequeStatement = ChequeStatement::onlyTrashed()->where('payment_id', $voucher->id)->first();
                if ($chequeStatement) {
                    $chequeStatement->restore();
                }
            }

            DB::commit();
            return response()->json(['success' => true, 'message' => 'Payment voucher restored successfully']);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['success' => false, 'message' => 'Error restoring voucher: ' . $e->getMessage()], 500);
        }
    }

    // Force delete a payment voucher transaction
    public function forceDelete($id)
    {
        $voucher = Payment::onlyTrashed()
            ->where('voucher_no', 'like', 'PAY-%')
            ->find($id);
        
        if (!$voucher) {
            return response()->json(['success' => false, 'message' => 'Voucher not found'], 404);
        }

        DB::beginTransaction();
        try {
            // Permanently delete related ChequeStatement if payment method is cheque
            if (strtolower($voucher->payment_method) === 'cheque') {
                $chequeStatement = ChequeStatement::onlyTrashed()->where('payment_id', $voucher->id)->first();
                if ($chequeStatement) {
                    $chequeStatement->forceDelete();
                }
            }

            // Permanently delete the voucher
            $voucher->forceDelete();

            DB::commit();
            return response()->json(['success' => true, 'message' => 'Payment voucher permanently deleted']);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['success' => false, 'message' => 'Error permanently deleting voucher: ' . $e->getMessage()], 500);
        }
    }
}