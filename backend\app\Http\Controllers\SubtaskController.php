<?php

namespace App\Http\Controllers;

use App\Models\Subtask;
use Illuminate\Http\Request;

class SubtaskController extends Controller
{
    public function index(Request $request)
    {
        $query = Subtask::with(['task', 'assignee']);
        if ($request->has('assignee_id')) {
            $query->where('assignee_id', $request->assignee_id);
        }
        return response()->json($query->get());
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'status' => 'required|string',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date',
            'task_id' => 'required|exists:tasks,id',
            'assignee_id' => 'required|exists:users,id',
        ]);
        $subtask = Subtask::create($validated);
        return response()->json($subtask->load(['task', 'assignee']), 201);
    }

    public function show($id)
    {
        $subtask = Subtask::with(['task', 'assignee'])->findOrFail($id);
        return response()->json($subtask);
    }

    public function update(Request $request, $id)
    {
        $subtask = Subtask::findOrFail($id);
        $validated = $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
            'status' => 'sometimes|required|string',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date',
            'task_id' => 'sometimes|required|exists:tasks,id',
            'assignee_id' => 'sometimes|required|exists:users,id',
        ]);
        if (isset($validated['status'])) {
            if (strtolower($validated['status']) === 'completed') {
                $validated['finished_at'] = now();
            } else {
                $validated['finished_at'] = null;
            }
        }
        $subtask->update($validated);
        return response()->json($subtask->load(['task', 'assignee']));
    }

    public function destroy($id)
    {
        $subtask = Subtask::findOrFail($id);
        $subtask->delete();
        return response()->json(['message' => 'Subtask deleted']);
    }
} 