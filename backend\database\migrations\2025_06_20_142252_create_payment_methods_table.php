<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_methods', function (Blueprint $table) {
            $table->id();
            $table->enum('type', ['Sales', 'Invoice', 'Purchase'])->index();
            $table->string('reference_number')->index(); // bill_number for Sales/Purchase, invoice_no for Invoice
            $table->enum('refer_type', ['Customer', 'Supplier']);
            $table->unsignedBigInteger('refer_id')->nullable()->index(); // customer_id or supplier_id
            $table->string('refer_name');
            $table->decimal('total', 15, 2);
            $table->string('payment_type'); // payment_type for Sales, payment_method for Invoice/Purchase
            $table->string('cheque_no')->nullable();
            $table->string('bank_name')->nullable();
            $table->date('issue_date')->nullable();
            $table->decimal('settled_amount', 15, 2); // received_amount for Sales, purchase_amount for Invoice, paid_amount for Purchase
            $table->decimal('balance_amount', 15, 2); // balance_amount for Sales, balance for Invoice, (total - paid_amount) for Purchase
            $table->date('date'); // created_at for Sales, invoice_date for Invoice, date_of_purchase for Purchase
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_methods');
    }
};
