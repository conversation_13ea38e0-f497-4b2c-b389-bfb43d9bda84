<?php

namespace App\Http\Controllers;

use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Illuminate\Support\Facades\Validator;

class ProductController extends Controller
{
    public function index(Request $request)
    {
        try {
            $query = Product::with('variants');

            if ($request->has('product_name')) {
                $productName = $request->query('product_name');
                $query->where('product_name', $productName);
            }

            // Add support for 'search' parameter for partial matching
            if ($request->has('search')) {
                $search = $request->query('search');
                $query->where('product_name', 'like', "%{$search}%");
            }

            $products = $query->get();

            return response()->json(['message' => 'Products fetched successfully', 'data' => $products], 200);
        } catch (\Exception $e) {
            return $this->handleException($e, 'Error fetching products');
        }
    }

    public function store(Request $request)
    {
        Log::info('Creating new product or batch:', $request->all());
        if ($request->has('product_id') || $request->has('id')) {
            Log::warning('Unexpected product_id or id in store request', [
                'product_id' => $request->input('product_id'),
                'id' => $request->input('id'),
            ]);
        }
        try {
            $validatedData = $request->validate($this->storeRules());

            // Handle extra_fields properly
            if (isset($validatedData['extra_fields']) && is_string($validatedData['extra_fields'])) {
                $decodedExtraFields = json_decode($validatedData['extra_fields'], true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $validatedData['extra_fields'] = $decodedExtraFields;
                } else {
                    Log::warning("Invalid JSON in extra_fields during product creation:", ['data' => $validatedData['extra_fields']]);
                    $validatedData['extra_fields'] = [];
                }
            }

            // Create product
            $product = Product::create($validatedData);

            // If batch-specific fields are provided, create variant
            $variantFields = ['batch_number', 'expiry_date', 'buying_cost', 'sales_price', 'minimum_price', 'wholesale_price', 'barcode', 'mrp', 'minimum_stock_quantity', 'opening_stock_quantity', 'opening_stock_value', 'store_location', 'cabinet', 'row'];
            $variantData = [];
            foreach ($variantFields as $field) {
                if ($request->has($field)) {
                    $variantData[$field] = $request->input($field);
                }
            }
            if (!empty($variantData)) {
                $variantData['product_id'] = $product->product_id;
                $product->variants()->create($variantData);
            }

            Log::info("Product created successfully", [
                'product_id' => $product->product_id,
                'product_name' => $product->product_name,
                'extra_fields' => $product->extra_fields
            ]);
            
            return response()->json(['message' => 'Product created successfully', 'data' => $product->load('variants')], 201);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::warning('Validation error in store method', ['errors' => $e->errors()]);
            return response()->json(['message' => 'Validation error', 'errors' => $e->errors()], 422);
        } catch (\Exception $e) {
            return $this->handleException($e, 'Error creating product or batch');
        }
    }

    public function addBatch(Request $request, $productId)
    {
        Log::info("Adding new batch for product ID: $productId", $request->all());
        try {
            // Enhanced validation: Ensure product exists and log details
            $product = Product::findOrFail($productId);
            Log::info("Found product for batch addition", [
                'product_id' => $product->product_id,
                'product_name' => $product->product_name,
                'requested_product_id' => $productId
            ]);

            // Additional validation: Check if product_id in request matches URL parameter
            if ($request->has('product_id') && $request->input('product_id') != $productId) {
                Log::warning("Product ID mismatch in batch addition", [
                    'url_product_id' => $productId,
                    'request_product_id' => $request->input('product_id')
                ]);
                return response()->json([
                    'message' => 'Product ID mismatch. Please ensure you are adding the batch to the correct product.',
                    'error' => 'Product ID in request does not match URL parameter'
                ], 422);
            }

            $validatedData = $request->validate([
                'batch_number' => 'required|string',
                'expiry_date' => 'nullable|date',
                'buying_cost' => 'nullable|numeric|min:0',
                'sales_price' => 'required|numeric|min:0',
                'minimum_price' => 'nullable|numeric|min:0',
                'wholesale_price' => 'nullable|numeric|min:0',
                'barcode' => 'nullable|string',
                'mrp' => 'required|numeric|min:0',
                'minimum_stock_quantity' => 'nullable|numeric|min:0',
                'opening_stock_quantity' => 'nullable|numeric|min:0',
                'opening_stock_value' => 'nullable|numeric|min:0',
                'store_location' => 'nullable|string',
                'cabinet' => 'nullable|string',
                'row' => 'nullable|string',
                'extra_fields' => 'nullable|string',
            ]);


            // Generate barcode if not provided
            if (empty($validatedData['barcode'])) {
                $validatedData['barcode'] = 'BAR' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
                while ($product->variants()->where('barcode', $validatedData['barcode'])->exists()) {
                    $validatedData['barcode'] = 'BAR' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
                }
            }

            // Check for existing batch with same batch number for this specific product
            $existingBatch = $product->variants()->where('batch_number', $validatedData['batch_number'])->first();
            if ($existingBatch) {
                Log::info("Updating existing batch", [
                    'product_id' => $product->product_id,
                    'batch_number' => $validatedData['batch_number'],
                    'variant_id' => $existingBatch->product_variant_id
                ]);
                // Update existing batch with new data
                $existingBatch->update($validatedData);
                return response()->json(['message' => 'Batch updated successfully', 'data' => $existingBatch], 200);
            }

            // Note: Allowing same batch number across different products
            // This is useful when batch numbers represent production dates or supplier batches
            // that can contain multiple different products
            Log::info("Batch number validation passed", [
                'batch_number' => $validatedData['batch_number'],
                'product_id' => $productId,
                'product_name' => $product->product_name
            ]);

            $validatedData['product_id'] = $product->product_id;

            if (isset($validatedData['extra_fields']) && is_string($validatedData['extra_fields'])) {
                $decodedExtraFields = json_decode($validatedData['extra_fields'], true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $validatedData['extra_fields'] = $decodedExtraFields;
                }
            }

            // Create new batch variant
            $productVariant = $product->variants()->create($validatedData);

            Log::info("New batch created successfully", [
                'product_id' => $product->product_id,
                'product_name' => $product->product_name,
                'batch_number' => $validatedData['batch_number'],
                'variant_id' => $productVariant->product_variant_id
            ]);

            return response()->json(['message' => 'New batch added successfully', 'data' => $productVariant], 201);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::warning('Validation error in addBatch method', ['errors' => $e->errors()]);
            return response()->json(['message' => 'Validation error', 'errors' => $e->errors()], 422);
        } catch (\Exception $e) {
            return $this->handleException($e, 'Error adding new batch');
        }
    }

    public function show($id)
    {
        try {
            $product = Product::with('variants')->findOrFail($id);
            
            // Debug logging
            Log::info("Product show - Product ID: $id");
            Log::info("Product show - Product extra_fields:", ['extra_fields' => $product->extra_fields]);
            Log::info("Product show - Product attributes extra_fields:", ['extra_fields' => $product->attributes['extra_fields'] ?? 'null']);
            Log::info("Product show - Variants count:", ['count' => $product->variants->count()]);
            
            if ($product->variants->count() > 0) {
                Log::info("Product show - First variant extra_fields:", ['extra_fields' => $product->variants->first()->extra_fields]);
            }
            
            // Convert to array to see the actual data structure
            $productArray = $product->toArray();
            Log::info("Product show - Final product array extra_fields:", ['extra_fields' => $productArray['extra_fields'] ?? 'null']);
            
            return response()->json(['message' => 'Product fetched successfully', 'data' => $product], 200);
        } catch (\Exception $e) {
            return $this->handleException($e, 'Product not found', 404);
        }
    }

    public function update(Request $request, $id)
    {
        // Product update method

        // Use proper validation rules
        $validatedData = $request->validate([
            'product_name' => 'required|string|max:255',
            'item_code' => 'nullable|string',
            'category' => 'nullable|string',
            'supplier' => 'nullable|string',
            'unit_type' => 'nullable|string',
            'store_location' => 'nullable|string',
            'cabinet' => 'nullable|string',
            'row' => 'nullable|string',
            'company' => 'nullable|string',
            'branch_name' => 'nullable|string',
            'branch_qty' => 'nullable|numeric',
            'extra_fields' => 'nullable|string',
            // Variant fields (for validation only)
            'batch_number' => 'nullable|string',
            'expiry_date' => 'nullable|date',
            'buying_cost' => 'nullable|numeric|min:0',
            'sales_price' => 'nullable|numeric|min:0',
            'minimum_price' => 'nullable|numeric|min:0',
            'wholesale_price' => 'nullable|numeric|min:0',
            'barcode' => 'nullable|string',
            'mrp' => 'nullable|numeric|min:0',
            'minimum_stock_quantity' => 'nullable|numeric|min:0',
            'opening_stock_quantity' => 'nullable|numeric|min:0',
            'opening_stock_value' => 'nullable|numeric|min:0',
        ]);

        Log::info("=== PRODUCT UPDATE START ===");
        Log::info("Updating product ID: $id");
        Log::info("Raw request data:", $request->all());

        try {
            $product = Product::with('variants')->findOrFail($id);
            Log::info("Found product:", ['id' => $product->product_id, 'name' => $product->product_name]);

            // Temporarily skip validation to test update logic
            $validatedData = $request->all();
            Log::info("Using all request data (validation skipped):", $validatedData);

            // Extract only product table fields (exclude variant fields)
            $productFields = [
                'product_name',
                'item_code',
                'category',
                'supplier',
                'unit_type',
                'store_location',
                'cabinet',
                'row',
                'company',
                'branch_name',
                'branch_qty',
                'extra_fields'
                // Note: minimum_stock_quantity removed - it's now in product_variants table
            ];

            $productData = array_intersect_key($validatedData, array_flip($productFields));

            // Clean up data types
            if (isset($productData['branch_qty'])) {
                $productData['branch_qty'] = is_numeric($productData['branch_qty']) ? (float)$productData['branch_qty'] : 0;
            }
            if (isset($productData['extra_fields']) && is_string($productData['extra_fields'])) {
                // Validate JSON
                $decoded = json_decode($productData['extra_fields'], true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    Log::warning("Invalid JSON in extra_fields:", ['data' => $productData['extra_fields']]);
                    $productData['extra_fields'] = []; // Default to empty array
                } else {
                    // Store the decoded array instead of the JSON string
                    $productData['extra_fields'] = $decoded;
                }
            }

            // Debug logging
            Log::info("Product update - Available product fields:", $productFields);
            Log::info("Product update - Validated data keys:", array_keys($validatedData));
            Log::info("Product update - Validated data:", $validatedData);
            Log::info("Product update - Product fields to update:", $productData);
            Log::info("Product update - Product data count:", ['validated' => count($validatedData), 'filtered' => count($productData)]);

            // Update product data with only valid product table fields
            try {
                $updateResult = $product->update($productData);
                Log::info("Product update result:", ['success' => $updateResult]);
            } catch (\Exception $updateException) {
                Log::error("Product update failed:", [
                    'error' => $updateException->getMessage(),
                    'data' => $productData
                ]);
                throw $updateException;
            }

            // Reload product to see changes
            $product->refresh();
            Log::info("Product after update:", [
                'id' => $product->product_id,
                'name' => $product->product_name,
                'item_code' => $product->item_code,
                'category' => $product->category,
                'supplier' => $product->supplier,
                'unit_type' => $product->unit_type
            ]);

            // Handle variant data update (removed store_location, cabinet, row as they belong to products table)
            $variantFields = ['batch_number', 'expiry_date', 'buying_cost', 'sales_price', 'minimum_price', 'wholesale_price', 'barcode', 'mrp', 'minimum_stock_quantity', 'opening_stock_quantity', 'opening_stock_value'];
            $variantData = [];
            foreach ($variantFields as $field) {
                if ($request->has($field)) {
                    $variantData[$field] = $request->input($field);
                }
            }

            // If variant data is provided, update the first variant or create one if none exists
            // BUT ONLY if we're not specifically editing a variant (check for editingVariantIndex in request)
            if (!empty($variantData) && !$request->has('editingVariantIndex')) {
                Log::info("Product update - Variant data to update:", $variantData);
                Log::info("Product update - Not editing specific variant, updating first variant");
                
                if ($product->variants && $product->variants->count() > 0) {
                    // Update the first variant with proper error handling
                    $variant = $product->variants->first();
                    Log::info("Product update - Updating existing variant ID:", ['variant_id' => $variant->product_variant_id]);
                    
                    try {
                        // Check for foreign key constraints before update
                        $saleItemsCount = DB::table('sale_items')->where('product_variant_id', $variant->product_variant_id)->count();
                        $purchaseItemsCount = DB::table('purchase_items')->where('product_variant_id', $variant->product_variant_id)->count();
                        $invoiceItemsCount = DB::table('invoice_items')->where('product_variant_id', $variant->product_variant_id)->count();
                        $salesReturnItemsCount = DB::table('sales_return_items')->where('product_variant_id', $variant->product_variant_id)->count();
                        $purchaseReturnItemsCount = DB::table('purchase_return_items')->where('product_variant_id', $variant->product_variant_id)->count();
                        $stockRechecksCount = DB::table('stock_rechecks')->where('product_variant_id', $variant->product_variant_id)->count();
                        
                        Log::info("Foreign key references for variant {$variant->product_variant_id}:", [
                            'sale_items' => $saleItemsCount,
                            'purchase_items' => $purchaseItemsCount,
                            'invoice_items' => $invoiceItemsCount,
                            'sales_return_items' => $salesReturnItemsCount,
                            'purchase_return_items' => $purchaseReturnItemsCount,
                            'stock_rechecks' => $stockRechecksCount
                        ]);
                        
                        // If variant has references, only update non-critical fields
                        if ($saleItemsCount > 0 || $purchaseItemsCount > 0 || $invoiceItemsCount > 0 || 
                            $salesReturnItemsCount > 0 || $purchaseReturnItemsCount > 0 || $stockRechecksCount > 0) {
                            
                            Log::warning("Variant {$variant->product_variant_id} has foreign key references, updating only safe fields");
                            
                            // Only update fields that don't affect foreign key relationships
                            $safeFields = ['buying_cost', 'sales_price', 'minimum_price', 'wholesale_price', 'mrp', 
                                         'minimum_stock_quantity', 'opening_stock_quantity', 'opening_stock_value'];
                            $safeVariantData = array_intersect_key($variantData, array_flip($safeFields));
                            
                            if (!empty($safeVariantData)) {
                                $variant->update($safeVariantData);
                                Log::info("Updated variant with safe fields only:", $safeVariantData);
                            } else {
                                Log::info("No safe fields to update for variant with foreign key references");
                            }
                        } else {
                            // No foreign key references, safe to update all fields
                            $variant->update($variantData);
                            Log::info("Updated variant with all fields:", $variantData);
                        }
                    } catch (\Exception $e) {
                        Log::error("Error updating variant {$variant->product_variant_id}:", [
                            'error' => $e->getMessage(),
                            'data' => $variantData
                        ]);
                        // Don't throw the error, just log it and continue
                        // The product update was successful, variant update failure shouldn't break the whole operation
                    }
                } else {
                    // Create a new variant if none exists
                    $variantData['product_id'] = $product->product_id;
                    Log::info("Product update - Creating new variant for product ID:", ['product_id' => $product->product_id]);
                    try {
                        $product->variants()->create($variantData);
                    } catch (\Exception $e) {
                        Log::error("Error creating new variant:", [
                            'error' => $e->getMessage(),
                            'data' => $variantData
                        ]);
                        // Don't throw the error, just log it and continue
                    }
                }
            } else {
                if ($request->has('editingVariantIndex')) {
                    Log::info("Product update - Skipping variant update because we're editing a specific variant");
                } else {
                    Log::info("Product update - No variant data to update");
                }
            }

            Log::info("=== PRODUCT UPDATE COMPLETE ===");
            return response()->json(['message' => 'Product updated successfully', 'data' => $product->load('variants')], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::warning("Validation failed for product ID: $id", ['errors' => $e->errors()]);
            return response()->json(['message' => 'Validation error', 'errors' => $e->errors()], 422);
        } catch (\Exception $e) {
            return $this->handleException($e, 'Error updating product');
        }
    }

    public function destroy(Request $request, $id)
    {
        try {
            $product = Product::with('variants')->findOrFail($id);

            // Check for foreign key constraints before deletion
            DB::beginTransaction();

            // Check if product is referenced in order_items (has restrict constraint)
            $orderItemsCount = DB::table('order_items')->where('product_id', $id)->count();
            if ($orderItemsCount > 0) {
                return response()->json([
                    'message' => 'Cannot delete product. It is referenced in ' . $orderItemsCount . ' purchase order(s). Please remove it from purchase orders first.'
                ], 409);
            }

            // Check if product is referenced in purchase_items
            $purchaseItemsCount = DB::table('purchase_items')->where('product_id', $id)->count();
            if ($purchaseItemsCount > 0) {
                return response()->json([
                    'message' => 'Cannot delete product. It is referenced in ' . $purchaseItemsCount . ' purchase(s). Please remove it from purchases first.'
                ], 409);
            }

            // Check if product is referenced in invoice_items
            $invoiceItemsCount = DB::table('invoice_items')->where('product_id', $id)->count();
            if ($invoiceItemsCount > 0) {
                return response()->json([
                    'message' => 'Cannot delete product. It is referenced in ' . $invoiceItemsCount . ' invoice(s). Please remove it from invoices first.'
                ], 409);
            }

            // Check if product is referenced in quotation_items
            $quotationItemsCount = DB::table('quotation_items')->where('product_id', $id)->count();
            if ($quotationItemsCount > 0) {
                return response()->json([
                    'message' => 'Cannot delete product. It is referenced in ' . $quotationItemsCount . ' quotation(s). Please remove it from quotations first.'
                ], 409);
            }

            // Check if product is referenced in stock_transfer_items
            $stockTransferItemsCount = DB::table('stock_transfer_items')->where('product_id', $id)->count();
            if ($stockTransferItemsCount > 0) {
                return response()->json([
                    'message' => 'Cannot delete product. It is referenced in ' . $stockTransferItemsCount . ' stock transfer(s). Please remove it from stock transfers first.'
                ], 409);
            }

            // Check if product is referenced in sales_return_items
            $salesReturnItemsCount = DB::table('sales_return_items')->where('product_id', $id)->count();
            if ($salesReturnItemsCount > 0) {
                return response()->json([
                    'message' => 'Cannot delete product. It is referenced in ' . $salesReturnItemsCount . ' sales return(s). Please remove it from sales returns first.'
                ], 409);
            }

            // Soft delete all variants (batches) first
            foreach ($product->variants as $variant) {
                $variant->deleted_by = $request->user()->id ?? auth()->id();
                $variant->save();
                $variant->delete(); // Soft delete
            }

            // Now soft delete the product itself
            $product->deleted_by = $request->user()->id ?? auth()->id();
            $product->save();
            $product->delete();

            DB::commit();

            return response()->json(['message' => 'Product and all batches deleted successfully'], 200);

        } catch (\Illuminate\Database\QueryException $e) {
            DB::rollBack();
            Log::error('Database error deleting product:', [
                'product_id' => $id,
                'message' => $e->getMessage(),
                'sql_error_code' => $e->getCode(),
                'trace' => $e->getTraceAsString()
            ]);

            // Check for foreign key constraint error
            if ($e->getCode() == '23000' || (isset($e->errorInfo[1]) && $e->errorInfo[1] == 1451)) {
                return response()->json([
                    'message' => 'Cannot delete product. It is referenced by other records. Please remove all references first.'
                ], 409);
            }

            return response()->json([
                'message' => 'Database error during deletion. Please check server logs.',
                'error' => $e->getMessage()
            ], 500);

        } catch (\Exception $e) {
            DB::rollBack();
            return $this->handleException($e, 'Error deleting product');
        }
    }

    public function import(Request $request)
    {
        Log::info('Import method called with request data: ', $request->all());
        try {
            $request->validate([
                'file' => 'required|mimes:xlsx,xls,csv',
            ]);

            if (!$request->hasFile('file')) {
                Log::warning('No file uploaded.');
                return response()->json(['message' => 'No file uploaded'], 400);
            }

            $file = $request->file('file');
            Log::info('File uploaded:', ['file' => $file->getClientOriginalName()]);
            $spreadsheet = IOFactory::load($file->getPathname());
            $sheet = $spreadsheet->getActiveSheet();
            $rows = $sheet->toArray();
            $header = array_shift($rows);
            Log::info('Excel header:', ['header' => $header]);

            DB::beginTransaction();
            $importedProducts = [];
            $rowCount = count($rows);
            Log::info('Total rows to process: ' . $rowCount);

            foreach ($rows as $index => $row) {
                Log::info("Processing row: $index", ['row' => $row]);
                if (empty($row[0])) {
                    Log::warning('Skipping row due to missing product_name:', ['index' => $index]);
                    continue;
                }

                $categoryName = $row[13] ?? null;
                $supplierName = $row[14] ?? null;
                $unitName = $row[15] ?? null;
                $storeLocationName = $row[16] ?? null;

                $categoryId = null;
                $supplierId = null;
                $unitId = null;
                $storeLocationId = null;

                if ($categoryName) {
                    $category = \App\Models\Category::firstOrCreate(['name' => $categoryName]);
                    $categoryId = $category->id;
                }

                if ($supplierName) {
                    $supplier = \App\Models\Supplier::firstOrCreate(
                        ['supplier_name' => $supplierName],
                        ['contact' => '', 'address' => '']
                    );
                    $supplierId = $supplier->id;
                }

                if ($unitName) {
                    $unit = \App\Models\Unit::firstOrCreate(['unit_name' => $unitName]);
                    $unitId = $unit->id;
                }

                if ($storeLocationName) {
                    $storeLocation = \App\Models\StoreLocation::firstOrCreate(
                        ['store_name' => $storeLocationName],
                        ['phone_number' => '', 'address' => '']
                    );
                    $storeLocationId = $storeLocation->id;
                }

                // Helper function to parse numeric values and handle empty vs zero values
                $parseNumeric = function($value) {
                    // Handle truly empty values (null, empty string, whitespace)
                    if ($value === null || $value === '' || trim($value) === '') {
                        return null; // Keep as null for empty cells
                    }

                    // Convert to float
                    $parsed = floatval($value);

                    // Return the actual numeric value (including 0)
                    // This allows products with 0 price to be imported
                    return $parsed;
                };

                $productData = [
                    'product_name' => trim($row[0] ?? ''),
                    'item_code' => !empty($row[1]) ? trim($row[1]) : null,
                    'sales_price' => $parseNumeric($row[5]),
                    'category' => $categoryName,
                    'supplier' => $supplierName,
                    'unit_type' => $unitName,
                    'extra_fields' => json_encode([
                        'extra_field_name' => !empty($row[19]) ? trim($row[19]) : null,
                        'extra_field_value' => !empty($row[20]) ? trim($row[20]) : null,
                    ]),
                ];

                $variantData = [
                    'batch_number' => !empty($row[2]) ? trim($row[2]) : null,
                    'expiry_date' => !empty($row[3]) ? $row[3] : null,
                    'buying_cost' => $parseNumeric($row[4]),
                    'sales_price' => $parseNumeric($row[5]),
                    'minimum_price' => $parseNumeric($row[6]),
                    'wholesale_price' => $parseNumeric($row[7]),
                    'barcode' => !empty($row[8]) ? trim($row[8]) : null,
                    'mrp' => $parseNumeric($row[9]),
                    'minimum_stock_quantity' => $parseNumeric($row[10]),
                    'opening_stock_quantity' => $parseNumeric($row[11]),
                    'opening_stock_value' => $parseNumeric($row[12]),
                    'store_location' => $storeLocationName,
                    'cabinet' => !empty($row[17]) ? trim($row[17]) : null,
                    'row' => !empty($row[18]) ? trim($row[18]) : null,
                    'extra_fields' => json_encode([
                        'extra_field_name' => !empty($row[19]) ? trim($row[19]) : null,
                        'extra_field_value' => !empty($row[20]) ? trim($row[20]) : null,
                    ]),
                ];

                $validator = Validator::make($productData, $this->importRules());

                if ($validator->fails()) {
                    Log::warning('Validation failed for row: ' . $index, ['errors' => $validator->errors()->all()]);
                    continue;
                }

                try {
                    // Ensure product_name is trimmed for consistent matching
                    $trimmedProductName = trim($productData['product_name']);
                    if (empty($trimmedProductName)) {
                        Log::warning("Skipping row due to empty product name after trimming:", ['index' => $index]);
                        continue;
                    }

                    $productData['product_name'] = $trimmedProductName;

                    // Check if product already exists
                    $product = Product::where('product_name', $trimmedProductName)->first();

                    if (!$product) {
                        // Create new product only if it doesn't exist
                        $product = Product::create($productData);
                        Log::info("New product created: {$trimmedProductName}");
                    } else {
                        Log::info("Product already exists: {$trimmedProductName}, adding batch only");
                    }

                    // Always create a variant record for each row (batch_number is nullable)
                    Log::info("Creating variant for {$trimmedProductName}:", [
                        'batch_number' => $variantData['batch_number'] ?? 'NULL',
                        'buying_cost' => $variantData['buying_cost'],
                        'sales_price' => $variantData['sales_price'],
                        'minimum_price' => $variantData['minimum_price'],
                        'wholesale_price' => $variantData['wholesale_price'],
                        'mrp' => $variantData['mrp']
                    ]);

                    // Create variant with nullable batch_number
                    if (!empty($variantData['batch_number'])) {
                        // Has batch number - use it as unique identifier
                        $variant = $product->variants()->updateOrCreate(
                            ['batch_number' => $variantData['batch_number']],
                            $variantData
                        );
                        Log::info("Variant with batch {$variantData['batch_number']} created/updated for product: {$trimmedProductName}");
                    } else {
                        // No batch number - create variant with null batch_number
                        // Use product_id as identifier since batch_number is null
                        $variant = $product->variants()->updateOrCreate(
                            ['product_id' => $product->product_id, 'batch_number' => null],
                            $variantData
                        );
                        Log::info("Variant with NULL batch_number created/updated for product: {$trimmedProductName}");
                    }

                    $importedProducts[] = $product;
                    Log::info("Product and variant imported for row: $index", ['product' => $product->product_name, 'batch' => $variantData['batch_number'] ?? 'No batch']);
                } catch (\Exception $e) {
                    Log::error("Error creating product or variant for row $index:", ['error' => $e->getMessage()]);
                }
            }

            DB::commit();
            Log::info('Import process completed. Total products imported: ' . count($importedProducts));
            return response()->json([
                'message' => 'Products imported successfully',
                'imported_products' => $importedProducts,
            ], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error importing products:', ['error' => $e->getMessage()]);
            return response()->json([
                'message' => 'Error importing products',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function checkNames(Request $request)
    {
        try {
            Log::info('checkNames called', ['names' => $request->query('names')]);
            $names = $request->query('names', []);
            if (!is_array($names) || empty($names)) {
                Log::warning('Invalid names parameter', ['names' => $names]);
                return response()->json(['message' => 'Names must be an array and cannot be empty'], 400);
            }

            // Normalize names for comparison (trim whitespace and handle case sensitivity)
            $normalizedNames = array_map(function($name) {
                return trim($name);
            }, $names);

            Log::info('Normalized names for checking', ['normalized_names' => $normalizedNames]);

            // Use case-insensitive and space-insensitive search to handle "hovite sl", "hovitesl", "HOVITE SL" as same
            $existingProducts = Product::where(function($query) use ($normalizedNames) {
                foreach ($normalizedNames as $name) {
                    // Normalize by removing all spaces and converting to lowercase for comparison
                    $normalizedName = strtolower(preg_replace('/\s+/', '', trim($name)));
                    $query->orWhereRaw('LOWER(REPLACE(TRIM(product_name), " ", "")) = ?', [$normalizedName]);
                }
            })->pluck('product_name')->map(function($name) {
                return trim($name); // Return trimmed names for consistent comparison
            })->toArray();

            Log::info('Case-insensitive search results', ['found_products' => $existingProducts]);

            // Additional debugging: show all products in database for comparison
            $allProducts = Product::pluck('product_name')->toArray();
            Log::info('All products in database', ['all_products' => $allProducts]);
            Log::info('Searching for', ['search_names' => $normalizedNames]);

            Log::info('checkNames response', ['existing' => $existingProducts]);
            return response()->json(['existing' => $existingProducts], 200);
        } catch (\Exception $e) {
            return $this->handleException($e, 'Error checking product names');
        }
    }

    public function checkItemCode(Request $request)
    {
        try {
            $itemCode = $request->query('item_code');
            if (!$itemCode) {
                return response()->json(['message' => 'Item code is required'], 400);
            }
            $exists = Product::where('item_code', $itemCode)->exists();
            return response()->json(['exists' => $exists], 200);
        } catch (\Exception $e) {
            return $this->handleException($e, 'Error checking item code');
        }
    }

    public function checkBarcode(Request $request)
    {
        try {
            $barcode = $request->query('barcode');
            if (!$barcode) {
                return response()->json(['message' => 'Barcode is required'], 400);
            }
            $exists = \App\Models\ProductVariant::where('barcode', $barcode)->exists();
            return response()->json(['exists' => $exists], 200);
        } catch (\Exception $e) {
            return $this->handleException($e, 'Error checking barcode');
        }
    }

    public function getDeletedProducts()
    {
        $products = Product::onlyTrashed()->with(['variants', 'deletedByUser'])->get();
        return response()->json(['data' => $products->map(function($product) {
            return array_merge($product->toArray(), [
                'deleted_by_user' => $product->deletedByUser ? [
                    'id' => $product->deletedByUser->id,
                    'name' => $product->deletedByUser->name,
                    'email' => $product->deletedByUser->email,
                ] : null,
            ]);
        })]);
    }

    public function restoreProduct($id)
    {
        $product = Product::onlyTrashed()->findOrFail($id);
        $product->restore();
        // Restore all soft-deleted variants for this product
        $product->variants()->onlyTrashed()->restore();
        return response()->json($product->load('variants'));
    }

    public function forceDeleteProduct($id)
    {
        $product = Product::onlyTrashed()->findOrFail($id);
        $product->forceDelete();
        return response()->noContent();
    }

    private function storeRules()
    {
        return [
            'product_name' => [
                'required',
                'string',
                'max:255',
                function ($attribute, $value, $fail) {
                    // Case-insensitive and space-insensitive unique check for product names
                    $normalizedValue = strtolower(preg_replace('/\s+/', '', trim($value)));
                    $exists = Product::whereRaw('LOWER(REPLACE(TRIM(product_name), " ", "")) = ?', [$normalizedValue])->exists();
                    if ($exists) {
                        $fail('A product with this name already exists (ignoring case and spaces).');
                    }
                },
            ],
            'item_code' => 'nullable|string|unique:products,item_code',
            'sales_price' => 'required|numeric|min:0',
            'category' => 'nullable|string',
            'supplier' => 'nullable|string',
            'unit_type' => 'nullable|string',
            'company' => 'nullable|string',
            'branch_name' => 'nullable|string',
            'branch_qty' => 'nullable|numeric|min:0',
            'extra_fields' => 'nullable|json',
        ];
    }

    private function updateRules($id)
    {
        return [
            'product_name' => 'required|string|max:255',
            'item_code' => [
                'nullable',
                'string',
                Rule::unique('products', 'item_code')->ignore($id, 'product_id'),
            ],
            // Removed sales_price and other batch fields that are now in product_variants table
            'category' => 'nullable|string',
            'supplier' => 'nullable|string',
            'unit_type' => 'nullable|string',
            'company' => 'nullable|string',
            'branch_name' => 'nullable|string',
            'branch_qty' => 'nullable|numeric|min:0',
            'extra_fields' => 'nullable|json',
            // Variant fields (for validation only, handled separately in update method)
            'batch_number' => 'nullable|string',
            'expiry_date' => 'nullable|date',
            'buying_cost' => 'nullable|numeric|min:0',
            'sales_price' => 'nullable|numeric|min:0',
            'minimum_price' => 'nullable|numeric|min:0',
            'wholesale_price' => 'nullable|numeric|min:0',
            'barcode' => 'nullable|string',
            'mrp' => 'nullable|numeric|min:0',
            'minimum_stock_quantity' => 'nullable|numeric|min:0',
            'opening_stock_quantity' => 'nullable|numeric|min:0',
            'opening_stock_value' => 'nullable|numeric|min:0',
            'store_location' => 'nullable|string',
            'cabinet' => 'nullable|string',
            'row' => 'nullable|string',
        ];
    }

    private function importRules()
    {
        return [
            'product_name' => 'required|string|max:255',
            'item_code' => 'nullable|string', // Removed unique constraint to allow duplicate item codes
            'sales_price' => 'nullable|numeric|min:0', // Made nullable to allow products without sales price
            'category' => 'nullable|string',
            'supplier' => 'nullable|string',
            'unit_type' => 'nullable|string',
            'extra_fields' => 'nullable|json',
        ];
    }

    private function handleException(\Exception $e, $message, $status = 500)
    {
        Log::error($message, [
            'error' => $e->getMessage(),
            'stack' => $e->getTraceAsString(),
        ]);
        return response()->json([
            'message' => $message,
            'error' => $e->getMessage(),
        ], $status);
    }
}