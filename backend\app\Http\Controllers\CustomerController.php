<?php
namespace App\Http\Controllers;

use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB; // Added DB facade import

class CustomerController extends Controller
{
    public function index()
    {
        $customers = Customer::all()->map(function ($customer) {
            if ($customer->photo) {
                $customer->photo_url = url(Storage::url($customer->photo));
                Log::debug("Customer photo URL", ['id' => $customer->id, 'photo_url' => $customer->photo_url]);
            }
            return $customer;
        });
        return response()->json(['data' => $customers], 200);
    }

    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'customer_name' => 'required|string|max:255',
                'email' => 'nullable|email|max:255',
                'phone' => 'required|string|max:20|unique:customers,phone',
                'address' => 'nullable|string|max:500',
                'nic_number' => 'nullable|string|max:20|unique:customers,nic_number',
                'openingbalance' => 'nullable|numeric',
                'card_name' => 'nullable|string|max:100',
                'card_types' => 'nullable|array',
                'card_types.*' => 'string|max:100',
                'valid_date' => 'nullable|date',
                'photo' => 'nullable|image|max:2048',
            ]);

            $data = $request->only([
                'customer_name',
                'email',
                'phone',
                'address',
                'nic_number',
                'openingbalance',
                'card_name',
                'card_types',
                'valid_date',
            ]);

            // Clean up empty strings to null for nullable fields
            $data['email'] = !empty($data['email']) ? $data['email'] : null;
            $data['address'] = !empty($data['address']) ? $data['address'] : null;
            $data['nic_number'] = !empty($data['nic_number']) ? $data['nic_number'] : null;
            $data['card_name'] = !empty($data['card_name']) ? $data['card_name'] : null;
            $data['valid_date'] = !empty($data['valid_date']) ? $data['valid_date'] : null;

            // Generate unique loyalty card number
            do {
                $loyaltyCardNumber = str_pad(mt_rand(100000, 999999), 6, '0', STR_PAD_LEFT);
            } while (Customer::where('loyalty_card_number', $loyaltyCardNumber)->exists());

            $data['loyalty_card_number'] = $loyaltyCardNumber;

            // Handle photo upload
            if ($request->hasFile('photo') && $request->file('photo')->isValid()) {
                // Ensure storage directory exists
                $storagePath = storage_path('app/public/customers');
                if (!file_exists($storagePath)) {
                    mkdir($storagePath, 0755, true);
                }
                
                $photoPath = $request->file('photo')->storeAs(
                    'public/customers',
                    time() . '_' . $request->file('photo')->getClientOriginalName()
                );
                $data['photo'] = str_replace('public/', '', $photoPath);
                Log::info("Photo stored", [
                    'path' => $data['photo'],
                    'url' => url(Storage::url($data['photo'])),
                    'file_exists' => Storage::exists($photoPath),
                ]);
            }

            // Ensure card_types is an array
            $data['card_types'] = $request->input('card_types', []);

            $customer = Customer::create($data);
            
            // Add photo URL to response
            if ($customer->photo) {
                $customer->photo_url = url(Storage::url($customer->photo));
            }

            return response()->json(['data' => $customer], 201);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error("Validation error in store customer", [
                'errors' => $e->errors(),
                'request' => $request->except('photo'),
            ]);
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            Log::error("Exception in store customer", [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->except('photo'),
            ]);
            return response()->json([
                'message' => 'Server error, please try again later',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function update(Request $request, $id)
    {
        try {
            $customer = Customer::findOrFail($id);

            $validated = $request->validate([
                'customer_name' => 'required|string|max:255',
                'email' => 'nullable|email|max:255',
                'phone' => 'required|string|max:20|unique:customers,phone,' . $id,
                'address' => 'nullable|string|max:500',
                'nic_number' => 'nullable|string|max:20|unique:customers,nic_number,' . $id,
                'openingbalance' => 'nullable|numeric',
                'card_name' => 'nullable|string|max:100',
                'card_types' => 'nullable|array',
                'card_types.*' => 'string|max:100',
                'valid_date' => 'nullable|date',
                'photo' => 'nullable|image|max:2048',
            ]);

            $data = $request->only([
                'customer_name',
                'email',
                'phone',
                'address',
                'nic_number',
                'openingbalance',
                'card_name',
                'card_types',
                'valid_date',
            ]);

            // Clean up empty strings to null for nullable fields
            $data['email'] = !empty($data['email']) ? $data['email'] : null;
            $data['address'] = !empty($data['address']) ? $data['address'] : null;
            $data['nic_number'] = !empty($data['nic_number']) ? $data['nic_number'] : null;
            $data['card_name'] = !empty($data['card_name']) ? $data['card_name'] : null;
            $data['valid_date'] = !empty($data['valid_date']) ? $data['valid_date'] : null;

            // Preserve existing loyalty card number
            $data['loyalty_card_number'] = $customer->loyalty_card_number;

            // Handle photo upload
            if ($request->hasFile('photo') && $request->file('photo')->isValid()) {
                // Delete old photo if exists
                if ($customer->photo) {
                    Storage::delete('public/' . $customer->photo);
                }
                
                // Ensure storage directory exists
                $storagePath = storage_path('app/public/customers');
                if (!file_exists($storagePath)) {
                    mkdir($storagePath, 0755, true);
                }
                
                $photoPath = $request->file('photo')->storeAs(
                    'public/customers',
                    time() . '_' . $request->file('photo')->getClientOriginalName()
                );
                $data['photo'] = str_replace('public/', '', $photoPath);
                Log::info("Photo updated", [
                    'path' => $data['photo'],
                    'url' => url(Storage::url($data['photo'])),
                    'file_exists' => Storage::exists($photoPath),
                ]);
            } else {
                $data['photo'] = $customer->photo;
            }

            // Ensure card_types is an array
            $data['card_types'] = $request->input('card_types', []);

            $customer->update($data);
            
            // Add photo URL to response
            if ($customer->photo) {
                $customer->photo_url = url(Storage::url($customer->photo));
            }

            return response()->json(['data' => $customer], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error("Validation error in update customer", [
                'errors' => $e->errors(),
                'customer_id' => $id,
                'request' => $request->except('photo'),
            ]);
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            Log::error("Exception in update customer", [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'customer_id' => $id,
                'request' => $request->except('photo'),
            ]);
            return response()->json([
                'message' => 'Server error, please try again later',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function destroy($id)
    {
        $customer = Customer::findOrFail($id);

        if ($customer->photo) {
            Storage::delete('public/' . $customer->photo);
        }

        // Accept deleted_by from request, fallback to authenticated user
        $deletedBy = request()->input('deleted_by') ?? request()->user()->id ?? auth()->id();
        if (!$deletedBy) {
            Log::warning('No user ID found when deleting customer', [
                'request_user' => request()->user(),
                'auth_id' => auth()->id(),
            ]);
            return response()->json(['message' => 'No authenticated or provided user found for delete action.'], 403);
        }
        $customer->deleted_by = $deletedBy;
        $customer->save();
        $customer->delete();

        return response()->json(['message' => 'Customer deleted successfully'], 200);
    }

    public function deleted()
    {
        $customers = Customer::onlyTrashed()->with('deletedByUser')->get();
        return response()->json([
            'data' => $customers->map(function($customer) {
                return array_merge($customer->toArray(), [
                    'deleted_by_user' => $customer->deletedByUser ? [
                        'id' => $customer->deletedByUser->id,
                        'name' => $customer->deletedByUser->name,
                        'email' => $customer->deletedByUser->email,
                    ] : null,
                ]);
            })
        ]);
    }

    public function show($id)
    {
        $customer = Customer::withTrashed()->with('deletedByUser')->findOrFail($id);
        return response()->json(['data' => $customer]);
    }

    public function restore($id)
    {
        try {
            DB::beginTransaction();
            $customer = Customer::onlyTrashed()->with('deletedByUser')->findOrFail($id);
            $customer->restore();
            // Reload relationships after restore
            $customer->refresh();
            $customer->load('deletedByUser');
            DB::commit();
            return response()->json([
                'success' => true,
                'message' => 'Customer restored successfully',
                'data' => $customer
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            DB::rollBack();
            return response()->json(['success' => false, 'message' => 'Customer not found or not deleted'], 404);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function forceDelete($id)
    {
        try {
            DB::beginTransaction();
            $customer = Customer::onlyTrashed()->findOrFail($id);
            $customer->forceDelete();
            DB::commit();
            return response()->json(['success' => true, 'message' => 'Customer permanently deleted']);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            DB::rollBack();
            return response()->json(['success' => false, 'message' => 'Customer not found or not deleted'], 404);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
}