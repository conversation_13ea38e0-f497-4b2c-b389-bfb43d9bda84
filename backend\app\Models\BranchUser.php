<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BranchUser extends Model
{
    protected $fillable = [
        'branch_id',
        'user_id',
        'role',
        'permissions',
        'assigned_at',
        'assigned_by',
        'is_active'
    ];

    protected $casts = [
        'permissions' => 'array',
        'assigned_at' => 'datetime',
        'is_active' => 'boolean'
    ];

    /**
     * Get the branch that this user is assigned to
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class, 'branch_id', 'branch_id');
    }

    /**
     * Get the user assigned to the branch
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who assigned this user to the branch
     */
    public function assignedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_by');
    }
}
