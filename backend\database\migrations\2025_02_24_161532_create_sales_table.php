<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSalesTable extends Migration
{
    public function up()
    {
        Schema::create('sales', function (Blueprint $table) {
            $table->id();
            $table->string('bill_number')->unique();
            $table->unsignedBigInteger('customer_id')->nullable();
            $table->string('customer_name');
            $table->decimal('subtotal', 10, 2);
            $table->decimal('discount', 10, 2);
            $table->decimal('tax', 10, 2)->default(0);
            $table->decimal('total', 10, 2);
            $table->decimal('cost_price', 10, 2)->nullable();
            $table->string('payment_type');
            $table->string('cheque_no')->nullable();
            $table->string('bank_name')->nullable();
            $table->date('issue_date')->nullable();
            $table->decimal('received_amount', 10, 2);
            $table->decimal('balance_amount', 10, 2);
            $table->string('status')->default('pending'); // pending, partial, paid
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('sales');
    }
}
