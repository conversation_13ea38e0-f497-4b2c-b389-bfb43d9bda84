<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ProductionCategory;

class ProductionCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            ['name' => 'Category A', 'batch_number' => 'BATCH001', 'is_active' => true],
            ['name' => 'Category B', 'batch_number' => 'BATCH002', 'is_active' => true],
            ['name' => 'Category C', 'batch_number' => 'BATCH003', 'is_active' => true],
        ];

        foreach ($categories as $category) {
            ProductionCategory::create($category);
        }
    }
}
