<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCorrectedClosingStockToStockRechecksTable extends Migration
{
    public function up()
    {
        Schema::table('stock_rechecks', function (Blueprint $table) {
            if (!Schema::hasColumn('stock_rechecks', 'corrected_closing_stock')) {
                $table->decimal('corrected_closing_stock', 10, 2)->nullable();
            }
        });
    }

    public function down()
    {
        Schema::table('stock_rechecks', function (Blueprint $table) {
            if (Schema::hasColumn('stock_rechecks', 'corrected_closing_stock')) {
                $table->dropColumn('corrected_closing_stock');
            }
        });
    }
}
