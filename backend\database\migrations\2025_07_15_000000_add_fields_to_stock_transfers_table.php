<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddFieldsToStockTransfersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('stock_transfers', function (Blueprint $table) {
            if (!Schema::hasColumn('stock_transfers', 'from_branch_id')) {
                $table->unsignedBigInteger('from_branch_id')->nullable()->after('branch_id');
            }
            if (!Schema::hasColumn('stock_transfers', 'received_date')) {
                $table->timestamp('received_date')->nullable()->after('status');
            }
            if (!Schema::hasColumn('stock_transfers', 'received_by')) {
                $table->unsignedBigInteger('received_by')->nullable()->after('received_date');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('stock_transfers', function (Blueprint $table) {
            if (Schema::hasColumn('stock_transfers', 'from_branch_id')) {
                $table->dropColumn('from_branch_id');
            }
            if (Schema::hasColumn('stock_transfers', 'received_date')) {
                $table->dropColumn('received_date');
            }
            if (Schema::hasColumn('stock_transfers', 'received_by')) {
                $table->dropColumn('received_by');
            }
        });
    }
}
