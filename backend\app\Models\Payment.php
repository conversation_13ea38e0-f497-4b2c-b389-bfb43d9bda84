<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Payment extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'voucher_no',
        'transaction_id',
        'transaction_type',
        'reference_no',
        'refer_type',
        'refer_id',
        'refer_name',
        'amount',
        'discount',
        'opening_balance',
        'payment_date',
        'payment_method',
        'cheque_no',
        'bank_name',
        'issue_date',
        'account_type',
        'note'
    ];

    protected $casts = [
        'payment_date' => 'date',
        'issue_date' => 'date',
        'amount' => 'decimal:2',
        'discount' => 'decimal:2',
        'opening_balance' => 'decimal:2',
    ];

    // Define polymorphic relation if needed
    public function transaction()
    {
        return $this->morphTo(null, 'transaction_type', 'transaction_id');
    }

    // Define relationships for refer_type
    public function customer()
    {
        return $this->belongsTo(Customer::class, 'refer_id')->when($this->refer_type === 'Customer');
    }

    public function supplier()
    {
        return $this->belongsTo(Supplier::class, 'refer_id')->when($this->refer_type === 'Supplier');
    }

    public function staffLedger()
    {
        return $this->belongsTo(StaffLedger::class, 'refer_id')->when($this->refer_type === 'Ledger');
    }

    public function deletedByUser()
    {
        return $this->belongsTo(User::class, 'deleted_by')->withTrashed();
    }

    /**
     * Generate next voucher number based on transaction type
     * @param string $transactionType - 'payment' for PAY- prefix, 'receive' for REC- prefix
     * @return string
     */
    public static function generateVoucherNumber($transactionType = 'payment')
    {
        $prefix = $transactionType === 'payment' ? 'PAY-' : 'REC-';

        // Find the last voucher number (including soft-deleted)
        $lastPayment = self::withTrashed()
            ->where('voucher_no', 'like', $prefix . '%')
            ->orderBy('voucher_no', 'desc')
            ->first();

        $lastNumber = 0;
        if ($lastPayment) {
            $lastVoucherNo = $lastPayment->voucher_no;
            $lastNumber = (int) substr($lastVoucherNo, strlen($prefix));
        }

        $nextNumber = $lastNumber + 1;
        $nextVoucherNo = $prefix . str_pad($nextNumber, 3, '0', STR_PAD_LEFT);

        // Double-check for duplicates (including soft-deleted)
        $existing = self::withTrashed()->where('voucher_no', $nextVoucherNo)->first();
        if ($existing) {
            if ($existing->trashed()) {
                $existing->forceDelete(); // Free up the number
            } else {
                // If active, increment and try again
                $nextNumber++;
                $nextVoucherNo = $prefix . str_pad($nextNumber, 3, '0', STR_PAD_LEFT);
            }
        }

        return $nextVoucherNo;
    }

    /**
     * Get next voucher number for payment vouchers
     * @return string
     */
    public static function getNextPaymentVoucherNumber()
    {
        return self::generateVoucherNumber('payment');
    }

    /**
     * Get next voucher number for receive vouchers
     * @return string
     */
    public static function getNextReceiveVoucherNumber()
    {
        return self::generateVoucherNumber('receive');
    }

    // Automatically set reference_no and refer fields when creating a payment
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($payment) {
            if (empty($payment->reference_no) || empty($payment->refer_type) || empty($payment->refer_id) || empty($payment->refer_name)) {
                $transaction = $payment->transaction;
                if ($transaction) {
                    if ($payment->transaction_type === 'sale') {
                        $payment->reference_no = $payment->reference_no ?: $transaction->bill_number;
                        $payment->refer_type = $payment->refer_type ?: 'Customer';
                        $payment->refer_id = $payment->refer_id ?: $transaction->customer_id;
                        $payment->refer_name = $payment->refer_name ?: $transaction->customer_name;
                    } elseif ($payment->transaction_type === 'invoice') {
                        $payment->reference_no = $payment->reference_no ?: $transaction->invoice_no;
                        $payment->refer_type = $payment->refer_type ?: 'Customer';
                        $payment->refer_id = $payment->refer_id ?: $transaction->customer_id;
                        $payment->refer_name = $payment->refer_name ?: $transaction->customer_name;
                    } elseif ($payment->transaction_type === 'purchase') {
                        $payment->reference_no = $payment->reference_no ?: $transaction->bill_number;
                        $payment->refer_type = $payment->refer_type ?: 'Supplier';
                        $payment->refer_id = $payment->refer_id ?: $transaction->supplier_id;
                        $payment->refer_name = $payment->refer_name ?: ($transaction->supplier ? $transaction->supplier->supplier_name : 'Unknown Supplier');
                    }
                }
            }
        });
    }
}
