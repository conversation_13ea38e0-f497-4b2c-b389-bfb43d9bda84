<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Sale extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [

        'bill_number', 'customer_id', 'customer_name', 'subtotal', 'discount', 'tax', 'total', 'payment_type', 'cheque_no', 'bank_name', 'issue_date', 'received_amount', 'balance_amount', 'status', 'deleted_by',
    ];

    protected $casts = [
        'issue_date' => 'date',
    ];

    public function items()
    {
        return $this->hasMany(SaleItem::class);
    }

    public function itemsWithTrashed()
    {
        return $this->hasMany(SaleItem::class)->withTrashed();
    }

    public function customer()
    {
        return $this->belongsTo(\App\Models\Customer::class, 'customer_id');
    }

    public function deletedByUser()
    {
        return $this->belongsTo(User::class, 'deleted_by')->withTrashed();
    }
}