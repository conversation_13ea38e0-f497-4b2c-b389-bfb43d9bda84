<?php

require_once __DIR__ . '/../../vendor/autoload.php';

use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once __DIR__ . '/../../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing Production Item API response...\n";

// Get all production items with their formulas and raw materials
$productionItems = DB::table('production_items')
    ->leftJoin('production_item_formulas', 'production_items.id', '=', 'production_item_formulas.production_item_id')
    ->leftJoin('raw_materials', 'production_item_formulas.raw_material_id', '=', 'raw_materials.id')
    ->select(
        'production_items.id as production_item_id',
        'production_items.name as production_item_name',
        'production_item_formulas.id as formula_id',
        'production_item_formulas.raw_material_id',
        'production_item_formulas.quantity',
        'production_item_formulas.price as formula_price',
        'raw_materials.name as raw_material_name',
        'raw_materials.cost_price as raw_material_cost_price'
    )
    ->get();

echo "Production Items with Formulas:\n";
foreach ($productionItems as $item) {
    echo "Production Item ID: {$item->production_item_id}, Name: {$item->production_item_name}\n";
    echo "  Formula ID: {$item->formula_id}, Raw Material ID: {$item->raw_material_id}\n";
    echo "  Raw Material Name: {$item->raw_material_name}\n";
    echo "  Formula Price: {$item->formula_price}\n";
    echo "  Raw Material Cost Price: {$item->raw_material_cost_price}\n";
    echo "  Quantity: {$item->quantity}\n";
    echo "---\n";
}

// Test the actual API response using the model
echo "\nTesting Model API Response:\n";
$productionItem = \App\Models\ProductionItem::with(['category', 'formulas.rawMaterial'])->first();

if ($productionItem) {
    echo "Production Item: " . json_encode($productionItem->toArray(), JSON_PRETTY_PRINT) . "\n";
} else {
    echo "No production items found.\n";
} 