<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Branch extends Model
{
    use HasFactory;
    
    protected $primaryKey = 'branch_id';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'branch_id',
        'branch_name',
        'branch_address',
        'phone_number',
        'email',
    ];

    /**
     * Get all users assigned to this branch
     */
    public function branchUsers(): HasMany
    {
        return $this->hasMany(BranchUser::class, 'branch_id', 'branch_id');
    }

    /**
     * Get all active users assigned to this branch
     */
    public function activeUsers(): HasMany
    {
        return $this->branchUsers()->where('is_active', true);
    }

    /**
     * Get users through the pivot table
     */
    public function users()
    {
        return $this->hasManyThrough(
            User::class,
            BranchUser::class,
            'branch_id', // Foreign key on branch_users table
            'id', // Foreign key on users table
            'branch_id', // Local key on branches table
            'user_id' // Local key on branch_users table
        )->where('branch_users.is_active', true);
    }
}
