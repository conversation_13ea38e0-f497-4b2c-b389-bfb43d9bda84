<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    public function up()
    {
        // Conditionally drop the unique index on barcode if it exists
        if (Schema::hasTable('product_variants')) {
            $indexName = 'product_variants_barcode_unique';
            $indexes = DB::select("SHOW INDEX FROM product_variants WHERE Key_name = '$indexName'");
            if (count($indexes) > 0) {
                DB::statement('ALTER TABLE product_variants DROP INDEX ' . $indexName);
            }
        }
    }

    public function down()
    {
        Schema::table('product_variants', function (Blueprint $table) {
            $table->unique('barcode');
        });
    }
}; 