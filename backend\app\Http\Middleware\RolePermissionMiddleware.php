<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RolePermissionMiddleware
{
    public function handle(Request $request, Closure $next, $permission = null): Response
    {
        \Log::info('RolePermissionMiddleware - Checking permissions for: ' . $request->route()->uri());
        $user = Auth::user();

        if (!$user) {
            \Log::error('No authenticated user in RolePermissionMiddleware');
            return response()->json(['message' => 'Unauthenticated'], 401);
        }

        // Allow superadmin or admin to bypass all checks
        if (
            $user->hasRole('superadmin') ||
            $user->hasRole('admin') ||
            in_array($user->role, ['admin', 'superadmin'])
        ) {
            \Log::info('User bypassed permission check', [
                'email' => $user->email,
                'roles' => $user->getRoleNames(),
                'table_role' => $user->role
            ]);
            return $next($request);
        }

        // Check specific permission if provided
        if ($permission && !$user->hasPermissionTo($permission)) {
            \Log::warning('User lacks specific permission', [
                'permission' => $permission,
                'email' => $user->email
            ]);
            return response()->json(['message' => 'Unauthorized action'], 403);
        }

        // Check module access based on route prefix
        $module = $this->getModuleFromRequest($request);
        if ($module && !$user->hasPermissionTo($module)) {
            \Log::warning('User lacks module permission', [
                'module' => $module,
                'email' => $user->email
            ]);
            return response()->json(['message' => 'Unauthorized access to this module'], 403);
        }

        return $next($request);
    }

    protected function getModuleFromRequest(Request $request): ?string
    {
        $routePrefix = explode('/', $request->route()->getPrefix())[0] ?? null;

        return match ($routePrefix) {
            'users' => 'UserList',
            'items' => 'items',
            'categories' => 'categories',
            'store-locations' => 'store-locations',
            'itemageanalyze' => 'itemageanalyze',
            'sales' => 'sales',
            'purchasing' => 'purchasing',
            'dashboard' => 'dashboard', // Explicitly map dashboard
            default => $routePrefix
        };
    }
}