<?php

namespace App\Http\Controllers;

use App\Models\BranchUser;
use Illuminate\Http\Request;

class DebugController extends Controller
{
    public function checkUserBranchAssignment($userId)
    {
        try {
            // Get all branch user assignments for this user
            $branchUsers = BranchUser::where('user_id', $userId)->get();
            
            // Get active branch user assignments
            $activeBranchUsers = BranchUser::where('user_id', $userId)
                ->where('is_active', true)
                ->get();
                
            return response()->json([
                'success' => true,
                'message' => 'User branch assignments',
                'data' => [
                    'all_assignments' => $branchUsers,
                    'active_assignments' => $activeBranchUsers
                ]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error checking user branch assignments',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
