<?php
namespace App\Http\Controllers;

use App\Models\Supplier;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SupplierController extends Controller
{
    // 1️⃣ Get All Suppliers (excluding deleted)
    public function index()
    {
        return response()->json(Supplier::all(), 200);
    }

    // 2️⃣ Create New Supplier
    public function store(Request $request)
    {
        $request->validate([
            'supplier_name' => 'required|string|max:255',
            'contact' => 'required|string|max:20',
            'address' => 'required|string|max:255',
            'opening_balance' => 'nullable|numeric|min:0',
        ]);

        $supplier = Supplier::create($request->all());

        return response()->json($supplier, 201);
    }

    // 3️⃣ Get Single Supplier (including trashed if needed)
    public function show($id)
    {
        $supplier = Supplier::withTrashed()->with('deletedByUser')->find($id);
        if (!$supplier) {
            return response()->json(['message' => 'Supplier not found'], 404);
        }
        return response()->json($supplier, 200);
    }

    // 4️⃣ Update Supplier
    public function update(Request $request, $id)
    {
        $supplier = Supplier::find($id);
        if (!$supplier) {
            return response()->json(['message' => 'Supplier not found'], 404);
        }

        $request->validate([
            'supplier_name' => 'required|string|max:255',
            'contact' => 'required|string|max:20',
            'address' => 'required|string|max:255',
            'opening_balance' => 'nullable|numeric|min:0',
        ]);

        $supplier->update($request->all());

        return response()->json($supplier, 200);
    }

    // 5️⃣ Soft Delete Supplier (move to recycle bin)
    public function destroy(Request $request, $id)
    {
        $supplier = Supplier::find($id);
        if (!$supplier) {
            return response()->json(['message' => 'Supplier not found'], 404);
        }
        // Accept deleted_by from request, fallback to authenticated user
        $deletedBy = $request->input('deleted_by') ?? $request->user()->id ?? auth()->id();
        $supplier->deleted_by = $deletedBy;
        $supplier->save();
        $supplier->delete();
        return response()->json(['message' => 'Supplier moved to recycle bin'], 200);
    }

    // 6️⃣ List deleted suppliers (recycle bin)
    public function deleted()
    {
        $suppliers = Supplier::onlyTrashed()->with('deletedByUser')->get();
        return response()->json(['data' => $suppliers], 200);
    }

    // 7️⃣ Restore supplier from recycle bin
    public function restore($id)
    {
        $supplier = Supplier::onlyTrashed()->find($id);
        if (!$supplier) {
            return response()->json(['message' => 'Supplier not found or not deleted'], 404);
        }
        $supplier->restore();
        return response()->json(['message' => 'Supplier restored successfully', 'data' => $supplier]);
    }

    // 8️⃣ Permanently delete supplier
    public function forceDelete($id)
    {
        $supplier = Supplier::onlyTrashed()->find($id);
        if (!$supplier) {
            return response()->json(['message' => 'Supplier not found or not deleted'], 404);
        }
        $supplier->forceDelete();
        return response()->json(['message' => 'Supplier permanently deleted']);
    }
}

