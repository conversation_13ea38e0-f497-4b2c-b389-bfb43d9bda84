<?php

require_once __DIR__ . '/../../vendor/autoload.php';

use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once __DIR__ . '/../../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Checking raw materials with missing or zero cost_price...\n";

$rawMaterials = DB::table('raw_materials')
    ->select('id', 'name', 'cost_price', 'selling_price')
    ->whereNull('cost_price')
    ->orWhere('cost_price', 0)
    ->get();

if ($rawMaterials->count() > 0) {
    echo "Found " . $rawMaterials->count() . " raw materials with missing or zero cost_price:\n";
    foreach ($rawMaterials as $material) {
        echo "ID: {$material->id}, Name: {$material->name}, Cost Price: " . ($material->cost_price ?? 'NULL') . "\n";
    }
} else {
    echo "All raw materials have valid cost_price values.\n";
}

echo "\nChecking all raw materials:\n";
$allMaterials = DB::table('raw_materials')
    ->select('id', 'name', 'cost_price', 'selling_price')
    ->get();

foreach ($allMaterials as $material) {
    echo "ID: {$material->id}, Name: {$material->name}, Cost Price: " . ($material->cost_price ?? 'NULL') . "\n";
} 