<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, migrate existing data to branch_users table
        $branches = DB::table('branches')
            ->whereNotNull('authorized_person')
            ->where('authorized_person', '!=', '')
            ->get();

        foreach ($branches as $branch) {
            $user = DB::table('users')
                ->where('username', $branch->authorized_person)
                ->first();

            if ($user) {
                DB::table('branch_users')->insertOrIgnore([
                    'branch_id' => $branch->branch_id,
                    'user_id' => $user->id,
                    'role' => $branch->role ?? 'Manager',
                    'assigned_at' => now(),
                    'is_active' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }

        // Then drop the columns
        Schema::table('branches', function (Blueprint $table) {
            $table->dropColumn(['authorized_person', 'role']);
        });
    }

    public function down(): void
    {
        Schema::table('branches', function (Blueprint $table) {
            $table->string('authorized_person')->nullable();
            $table->string('role')->nullable();
        });
    }
};
