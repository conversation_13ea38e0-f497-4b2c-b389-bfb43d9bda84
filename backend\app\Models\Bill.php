<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Bill extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = ['customer_id', 'bill_no', 'payment_type', 'received_amount', 'total_amount'];

    public function items()
    {
        return $this->hasMany(BillItem::class);
    }

    public function getDeletedProducts()
    {
        $products = Product::onlyTrashed()->with('variants')->get();
        return response()->json(['data' => $products]);
    }
}
