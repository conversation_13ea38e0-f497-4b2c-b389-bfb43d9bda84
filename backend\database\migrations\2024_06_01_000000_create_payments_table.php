<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePaymentsTable extends Migration
{
    public function up()
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->string('voucher_no')->unique();
            $table->string('transaction_type'); // e.g., 'purchase', 'sale', 'invoice'
            $table->unsignedBigInteger('transaction_id');
            $table->string('reference_no')->nullable();
            $table->enum('refer_type', ['Customer', 'Supplier', 'Ledger'])->nullable(); // Customer, Supplier or Ledger
            $table->unsignedBigInteger('refer_id')->nullable(); // customer_id or supplier_id
            $table->string('refer_name')->nullable(); // customer_name or supplier_name
            $table->decimal('amount', 10, 2);
            $table->decimal('discount', 10, 2);
            $table->date('payment_date');
            $table->string('payment_method'); // e.g., 'Cash', 'Card', 'Cheque'
            $table->string('cheque_no')->nullable();
            $table->string('bank_name')->nullable();
            $table->date('issue_date')->nullable();
            $table->string('account_type')->nullable(); // For ledger transactions
            $table->text('note')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index(['transaction_type', 'transaction_id']);
            $table->index('voucher_no');
            $table->index(['refer_type', 'refer_id']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('payments');
    }
}
