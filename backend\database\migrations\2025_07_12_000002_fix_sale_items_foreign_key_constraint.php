<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sale_items', function (Blueprint $table) {
            // Drop the existing foreign key constraint
            $table->dropForeign(['sale_id']);
            
            // Add the foreign key constraint with proper handling for soft deletes
            $table->foreign('sale_id')->references('id')->on('sales')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sale_items', function (Blueprint $table) {
            // Drop the foreign key constraint
            $table->dropForeign(['sale_id']);
            
            // Re-add the original constraint
            $table->foreign('sale_id')->references('id')->on('sales')->onDelete('cascade');
        });
    }
}; 