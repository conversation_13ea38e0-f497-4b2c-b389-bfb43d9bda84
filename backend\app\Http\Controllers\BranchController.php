<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\BranchUser;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class BranchController extends Controller
{
    public function index()
    {
        try {
            $branches = Branch::with(['branchUsers.user'])->get();
            
            // Format the data to include user information
            $formattedBranches = $branches->map(function ($branch) {
                $branchData = $branch->toArray();
                $branchData['users'] = $branch->branchUsers->map(function ($branchUser) {
                    return [
                        'id' => $branchUser->user->id,
                        'username' => $branchUser->user->username,
                        'name' => $branchUser->user->name,
                        'email' => $branchUser->user->email,
                        'role' => $branchUser->role,
                        'permissions' => $branchUser->permissions,
                        'is_active' => $branchUser->is_active,
                        'assigned_at' => $branchUser->assigned_at,
                        'branch_user_id' => $branchUser->id,
                    ];
                });
                return $branchData;
            });

            return response()->json([
                'data' => $formattedBranches,
                'message' => 'Branches retrieved successfully',
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to fetch branches',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function show($id)
    {
        try {
            $branch = Branch::where('branch_id', $id)->firstOrFail();
            return response()->json([
                'data' => $branch,
                'message' => 'Branch retrieved successfully',
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Branch not found',
            ], 404);
        }
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'branchName' => 'required|string|max:255|min:2|unique:branches,branch_name',
            'branchAddress' => 'required|string|max:255',
            'phoneNumber' => 'required|string|max:20|unique:branches,phone_number',
            'email' => 'nullable|email|max:255|unique:branches,email',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $prefix = 'IMSS' . Str::upper(substr($request->branchName, 0, 2));
            $existingBranches = Branch::where('branch_id', 'like', $prefix . '%')->count();
            $branchId = $prefix . str_pad($existingBranches + 1, 3, '0', STR_PAD_LEFT);

            $attempts = 0;
            $maxAttempts = 100;
            while (Branch::where('branch_id', $branchId)->exists() && $attempts < $maxAttempts) {
                $existingBranches++;
                $branchId = $prefix . str_pad($existingBranches + 1, 3, '0', STR_PAD_LEFT);
                $attempts++;
            }

            if ($attempts >= $maxAttempts) {
                return response()->json([
                    'message' => 'Failed to generate unique branch ID',
                ], 500);
            }

            $branch = Branch::create([
                'branch_id' => $branchId,
                'branch_name' => $request->branchName,
                'branch_address' => $request->branchAddress,
                'phone_number' => $request->phoneNumber,
                'email' => $request->email,
            ]);

            // Load relationships for response
            $branch->load(['branchUsers.user']);

            return response()->json([
                'data' => $branch,
                'message' => 'Branch created successfully',
            ], 201);
        } catch (\Exception $e) {
            \Log::error('Branch creation failed: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to create branch',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'branchName' => 'required|string|max:255|min:2|unique:branches,branch_name,' . $id . ',branch_id',
            'branchAddress' => 'required|string|max:255',
            'phoneNumber' => 'required|string|max:20|unique:branches,phone_number,' . $id . ',branch_id',
            'email' => 'nullable|email|max:255|unique:branches,email,' . $id . ',branch_id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $branch = Branch::where('branch_id', $id)->firstOrFail();

            $newBranchId = $branch->branch_id;
            if ($request->branchName !== $branch->branch_name) {
                $prefix = 'IMSS' . Str::upper(substr($request->branchName, 0, 2));
                $existingBranches = Branch::where('branch_id', 'like', $prefix . '%')->count();
                $newBranchId = $prefix . str_pad($existingBranches + 1, 3, '0', STR_PAD_LEFT);

                $attempts = 0;
                $maxAttempts = 100;
                while (Branch::where('branch_id', $newBranchId)->where('branch_id', '!=', $id)->exists() && $attempts < $maxAttempts) {
                    $existingBranches++;
                    $newBranchId = $prefix . str_pad($existingBranches + 1, 3, '0', STR_PAD_LEFT);
                    $attempts++;
                }

                if ($attempts >= $maxAttempts) {
                    return response()->json([
                        'message' => 'Failed to generate unique branch ID',
                    ], 500);
                }
            }

            $branch->update([
                'branch_id' => $newBranchId,
                'branch_name' => $request->branchName,
                'branch_address' => $request->branchAddress,
                'phone_number' => $request->phoneNumber,
                'email' => $request->email,
            ]);

            // Load relationships for response
            $branch->load(['branchUsers.user']);

            return response()->json([
                'data' => $branch,
                'message' => 'Branch updated successfully',
            ], 200);
        } catch (\Exception $e) {
            \Log::error('Branch update failed: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to update branch',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function destroy($id)
    {
        try {
            $branch = Branch::where('branch_id', $id)->firstOrFail();
            $branch->delete();
            return response()->json([
                'message' => 'Branch deleted successfully',
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete branch',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    // Branch Users Management Methods

    /**
     * Get all users assigned to a branch
     */
    public function getBranchUsers($branchId)
    {
        try {
            $branch = Branch::where('branch_id', $branchId)->firstOrFail();
            $branchUsers = BranchUser::with(['user', 'assignedBy'])
                ->where('branch_id', $branchId)
                ->get(); // Remove is_active filter to get both active and inactive users

            $formattedUsers = $branchUsers->map(function ($branchUser) {
                return [
                    'id' => $branchUser->id,
                    'user_id' => $branchUser->user->id,
                    'username' => $branchUser->user->username,
                    'name' => $branchUser->user->name,
                    'email' => $branchUser->user->email,
                    'role' => $branchUser->role,
                    'permissions' => $branchUser->permissions,
                    'is_active' => $branchUser->is_active, // Add is_active status
                    'assigned_at' => $branchUser->assigned_at,
                    'assigned_by' => $branchUser->assignedBy ? $branchUser->assignedBy->username : null,
                ];
            });

            return response()->json([
                'data' => $formattedUsers,
                'message' => 'Branch users retrieved successfully',
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to fetch branch users',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Add a user to a branch
     */
    public function addUserToBranch(Request $request, $branchId)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'role' => 'required|string|max:255',
            'permissions' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $branch = Branch::where('branch_id', $branchId)->firstOrFail();
            $user = User::findOrFail($request->user_id);

            // Check if user is already assigned to this branch
            $existingAssignment = BranchUser::where('branch_id', $branchId)
                ->where('user_id', $request->user_id)
                ->first();

            if ($existingAssignment) {
                if ($existingAssignment->is_active) {
                    return response()->json([
                        'message' => 'User is already assigned to this branch',
                    ], 422);
                } else {
                    // Reactivate existing assignment
                    $existingAssignment->update([
                        'role' => $request->role,
                        'permissions' => $request->permissions,
                        'is_active' => true,
                        'assigned_at' => now(),
                        'assigned_by' => Auth::id(),
                    ]);
                    $branchUser = $existingAssignment;
                }
            } else {
                // Create new assignment
                $branchUser = BranchUser::create([
                    'branch_id' => $branchId,
                    'user_id' => $request->user_id,
                    'role' => $request->role,
                    'permissions' => $request->permissions,
                    'assigned_at' => now(),
                    'assigned_by' => Auth::id(),
                    'is_active' => true,
                ]);
            }

            $branchUser->load(['user', 'assignedBy']);

            return response()->json([
                'data' => [
                    'id' => $branchUser->id,
                    'user_id' => $branchUser->user->id,
                    'username' => $branchUser->user->username,
                    'name' => $branchUser->user->name,
                    'email' => $branchUser->user->email,
                    'role' => $branchUser->role,
                    'permissions' => $branchUser->permissions,
                    'assigned_at' => $branchUser->assigned_at,
                    'assigned_by' => $branchUser->assignedBy ? $branchUser->assignedBy->username : null,
                ],
                'message' => 'User assigned to branch successfully',
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to assign user to branch',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update a branch user's role or permissions
     */
    public function updateBranchUser(Request $request, $branchId, $userId)
    {
        $validator = Validator::make($request->all(), [
            'role' => 'required|string|max:255',
            'permissions' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $branchUser = BranchUser::where('branch_id', $branchId)
                ->where('user_id', $userId)
                ->where('is_active', true)
                ->firstOrFail();

            $branchUser->update([
                'role' => $request->role,
                'permissions' => $request->permissions,
            ]);

            $branchUser->load(['user', 'assignedBy']);

            return response()->json([
                'data' => [
                    'id' => $branchUser->id,
                    'user_id' => $branchUser->user->id,
                    'username' => $branchUser->user->username,
                    'name' => $branchUser->user->name,
                    'email' => $branchUser->user->email,
                    'role' => $branchUser->role,
                    'permissions' => $branchUser->permissions,
                    'assigned_at' => $branchUser->assigned_at,
                    'assigned_by' => $branchUser->assignedBy ? $branchUser->assignedBy->username : null,
                ],
                'message' => 'Branch user updated successfully',
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update branch user',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove a user from a branch
     */
    public function removeUserFromBranch($branchId, $userId)
    {
        try {
            $branchUser = BranchUser::where('branch_id', $branchId)
                ->where('user_id', $userId)
                ->where('is_active', true)
                ->firstOrFail();

            $branchUser->update(['is_active' => false]);

            return response()->json([
                'message' => 'User removed from branch successfully',
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to remove user from branch',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Toggle user status in branch (activate/deactivate)
     */
    public function toggleUserStatus($branchId, $userId)
    {
        try {
            $branchUser = BranchUser::where('branch_id', $branchId)
                ->where('user_id', $userId)
                ->firstOrFail();

            $branchUser->update(['is_active' => !$branchUser->is_active]);

            $status = $branchUser->is_active ? 'activated' : 'deactivated';
            
            return response()->json([
                'message' => "User {$status} successfully",
                'data' => [
                    'is_active' => $branchUser->is_active
                ]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to toggle user status',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Permanently delete user from branch
     */
    public function permanentlyRemoveUserFromBranch($branchId, $userId)
    {
        try {
            $branchUser = BranchUser::where('branch_id', $branchId)
                ->where('user_id', $userId)
                ->firstOrFail();

            $branchUser->delete();

            return response()->json([
                'message' => 'User permanently removed from branch successfully',
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to permanently remove user from branch',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get users that can be assigned to a branch (not currently assigned)
     */
    public function getAvailableUsers($branchId)
    {
        try {
            $branch = Branch::where('branch_id', $branchId)->firstOrFail();
            
            // Get users that are not already assigned to this branch
            $assignedUserIds = BranchUser::where('branch_id', $branchId)
                ->where('is_active', true)
                ->pluck('user_id');

            $availableUsers = User::whereNotIn('id', $assignedUserIds)
                ->where('is_active', true)
                ->select('id', 'username', 'name', 'email', 'role')
                ->with('roles')
                ->get()
                ->append('role_names');

            return response()->json([
                'data' => $availableUsers,
                'message' => 'Available users retrieved successfully',
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to fetch available users',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}